// Gemini AI 搜索助手
class GeminiSearchAssistant {
    constructor(config) {
        this.config = config.gemini || {};
        this.enabled = config.search?.enableAI ?? false;
        this.fallbackEnabled = config.search?.fallbackToTraditional ?? true;
        this.chatHistory = []; // 存储对话历史
        this.dataManager = null; // FAQ数据管理器引用
    }

    // 设置数据管理器引用
    setDataManager(dataManager) {
        this.dataManager = dataManager;
    }

    // 获取助手状态
    getStatus() {
        return {
            available: this.isAvailable(),
            model: this.config.model || 'gemini-pro',
            enabled: this.enabled,
            dataManagerConnected: !!this.dataManager,
            chatHistoryLength: this.chatHistory.length
        };
    }
    
    // 检查是否可用
    isAvailable() {
        return this.enabled && this.config.apiKey && !this.config.apiKey.includes('XXXXX');
    }
    
    // 增强搜索查询
    async enhanceSearchQuery(query, language = 'zh') {
        if (!this.isAvailable()) {
            console.debug('Gemini assistant not available');
            return { enhanced: false, query: query };
        }
        
        try {
            const prompt = this.buildEnhancePrompt(query, language);
            console.debug('Gemini prompt:', prompt.substring(0, 100) + '...');

            console.debug('Using standard API');
            const response = await this.callGeminiAPI(prompt);

            if (response && response.keywords) {
                console.debug('Gemini enhancement successful:', response);
                return {
                    enhanced: true,
                    originalQuery: query,
                    enhancedQuery: response.keywords.join(' '),
                    keywords: response.keywords,
                    intent: response.intent,
                    suggestions: response.suggestions || []
                };
            }
        } catch (error) {
            // 静默处理错误，不在控制台显示警告
            console.debug('Gemini search enhancement failed:', error.message);
        }
        
        return { enhanced: false, query: query };
    }
    
    // 智能对话方法 - 与FAQ数据集成
    async chat(message, language = 'zh') {
        console.log(`💬 开始处理对话: "${message}" (语言: ${language})`);

        // 添加到对话历史
        this.addToChatHistory('user', message, language);

        // 首先尝试从FAQ数据中查找直接匹配
        const faqResults = await this.searchFAQData(message, language);

        let response = '';

        if (faqResults.directMatch) {
            // 找到直接匹配的FAQ，返回详细答案
            console.log(`✅ 找到直接匹配的FAQ: ${faqResults.directMatch.id}`);
            response = this.formatFAQResponse(faqResults.directMatch, language, faqResults.relatedQuestions);
        } else if (faqResults.relatedQuestions.length > 0) {
            // 找到相关FAQ，使用AI生成基于FAQ知识库的回答
            console.log(`🔍 找到 ${faqResults.relatedQuestions.length} 个相关FAQ`);

            if (this.isAvailable()) {
                try {
                    response = await this.generateFAQBasedResponse(message, faqResults.relatedQuestions, language);
                } catch (error) {
                    console.warn('AI回答生成失败，使用FAQ搜索结果:', error);
                    response = this.formatSearchResults(faqResults.relatedQuestions, language);
                }
            } else {
                // AI不可用时，返回搜索结果
                response = this.formatSearchResults(faqResults.relatedQuestions, language);
            }
        } else {
            // 没有找到相关FAQ，返回引导信息
            response = this.generateGuidanceResponse(language);
        }

        // 添加助手回复到对话历史
        this.addToChatHistory('assistant', response, language);

        return response;
    }

    // 添加到对话历史
    addToChatHistory(role, content, language) {
        this.chatHistory.push({
            role,
            content,
            language,
            timestamp: new Date().toISOString()
        });

        // 保持对话历史在合理长度内（最多10轮对话）
        if (this.chatHistory.length > 20) {
            this.chatHistory = this.chatHistory.slice(-20);
        }
    }

    // 获取对话上下文
    getChatContext(language) {
        return this.chatHistory
            .filter(item => item.language === language)
            .slice(-6) // 最近3轮对话
            .map(item => `${item.role}: ${item.content}`)
            .join('\n');
    }

    // 清除对话历史
    clearChatHistory() {
        this.chatHistory = [];
    }

    // 智能搜索建议
    async getSearchSuggestions(query, availableQuestions, language = 'zh') {
        if (!this.isAvailable()) {
            return [];
        }

        try {
            const prompt = this.buildSuggestionPrompt(query, availableQuestions, language);
            const response = await this.callGeminiAPI(prompt);

            if (response && response.suggestions) {
                return response.suggestions;
            }
        } catch (error) {
            console.warn('Gemini suggestions failed:', error);
        }

        return [];
    }
    
    // 搜索FAQ数据
    async searchFAQData(query, language) {
        if (!this.dataManager) {
            console.warn('DataManager未设置，无法搜索FAQ数据');
            return { directMatch: null, relatedQuestions: [] };
        }

        try {
            // 使用DataManager搜索相关问题
            const searchResults = await this.dataManager.searchQuestions(query, language);
            console.log(`🔍 FAQ搜索结果: ${searchResults.length} 个问题`);

            let directMatch = null;
            const relatedQuestions = [];

            // 分析搜索结果，寻找直接匹配
            for (const result of searchResults.slice(0, 10)) { // 只处理前10个结果
                const question = result.question;
                const score = result.score || 0;

                // 如果相似度很高，认为是直接匹配
                if (score > 0.8 || this.isDirectMatch(query, question.title[language])) {
                    directMatch = question;
                    break;
                } else {
                    relatedQuestions.push(question);
                }
            }

            return {
                directMatch,
                relatedQuestions: relatedQuestions.slice(0, 5) // 最多5个相关问题
            };

        } catch (error) {
            console.error('FAQ数据搜索失败:', error);
            return { directMatch: null, relatedQuestions: [] };
        }
    }

    // 判断是否为直接匹配
    isDirectMatch(query, title) {
        const queryLower = query.toLowerCase().trim();
        const titleLower = title.toLowerCase().trim();

        // 简单的直接匹配逻辑
        return queryLower === titleLower ||
               titleLower.includes(queryLower) ||
               queryLower.includes(titleLower);
    }

    // 构建增强搜索的提示词
    buildEnhancePrompt(query, language) {
        const langMap = {
            'zh': '中文',
            'en': '英文',
            'ms': '马来文'
        };

        return `你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："${query}"，语言是${langMap[language]}。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用户的搜索意图
3. 可能的搜索建议

请以JSON格式回复：
{
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "intent": "用户搜索意图描述",
    "suggestions": ["建议1", "建议2", "建议3"]
}

专注于司机相关的术语，如：注册、APP使用、订单、支付、评价、车辆、安全等主题。`;
    }
    
    // 格式化FAQ直接匹配响应
    formatFAQResponse(faqQuestion, language, relatedQuestions = []) {
        const categories = this.dataManager ? this.dataManager.getCategories() : {};
        const category = categories[faqQuestion.category];
        const categoryName = category ? category.name[language] : '未知分类';
        const categoryIcon = category ? category.icon : '📋';

        let response = `✅ **${faqQuestion.title[language]}**\n\n`;
        response += `📋 **问题编号**: ${faqQuestion.id}\n`;
        response += `${categoryIcon} **分类**: ${categoryName}\n\n`;
        response += `📝 **详细解答**:\n${faqQuestion.content[language]}\n\n`;

        // 添加相关问题推荐
        if (relatedQuestions.length > 0) {
            response += `🔗 **相关问题推荐**:\n`;
            relatedQuestions.slice(0, 3).forEach((q, index) => {
                response += `${index + 1}. ${q.title[language]} (${q.id})\n`;
            });
        }

        return response;
    }

    // 格式化搜索结果响应
    formatSearchResults(questions, language) {
        if (questions.length === 0) {
            return this.generateGuidanceResponse(language);
        }

        let response = `🔍 我找到了以下相关信息:\n\n`;

        questions.slice(0, 3).forEach((question, index) => {
            const categories = this.dataManager ? this.dataManager.getCategories() : {};
            const category = categories[question.category];
            const categoryIcon = category ? category.icon : '📋';

            response += `${index + 1}. **${question.title[language]}** (${question.id})\n`;
            response += `   ${categoryIcon} ${category ? category.name[language] : '未知分类'}\n`;
            response += `   ${question.content[language].substring(0, 100)}...\n\n`;
        });

        response += `💡 点击问题编号可查看完整答案，或继续提问获取更多帮助。`;
        return response;
    }

    // 生成引导响应
    generateGuidanceResponse(language) {
        const responses = {
            'zh': `😊 抱歉，我没有找到直接相关的FAQ答案。

🔍 **建议您可以尝试**:
• 使用更具体的关键词，如"登录问题"、"提现流程"等
• 浏览不同分类查找相关问题
• 联系客服获取人工帮助

📋 **常见问题分类**:
📱 技术问题 | 💰 财务问题 | 🛎️ 服务流程
🚗 注册入门 | 💬 沟通管理 | 🚨 紧急处理`,

            'en': `😊 Sorry, I couldn't find directly related FAQ answers.

🔍 **You can try**:
• Use more specific keywords like "login issues", "withdrawal process"
• Browse different categories for related questions
• Contact customer service for manual assistance

📋 **Common Question Categories**:
📱 Technical | 💰 Financial | 🛎️ Service Process
🚗 Registration | 💬 Communication | 🚨 Emergency`,

            'ms': `😊 Maaf, saya tidak dapat mencari jawapan FAQ yang berkaitan secara langsung.

🔍 **Anda boleh cuba**:
• Gunakan kata kunci yang lebih spesifik seperti "masalah log masuk", "proses pengeluaran"
• Layari kategori berbeza untuk soalan berkaitan
• Hubungi khidmat pelanggan untuk bantuan manual

📋 **Kategori Soalan Biasa**:
📱 Teknikal | 💰 Kewangan | 🛎️ Proses Perkhidmatan
🚗 Pendaftaran | 💬 Komunikasi | 🚨 Kecemasan`
        };

        return responses[language] || responses['zh'];
    }

    // 构建建议提示词
    buildSuggestionPrompt(query, availableQuestions, language) {
        const questionSample = availableQuestions.slice(0, 20).map(q =>
            `- ${q.id}: ${q.title[language]}`
        ).join('\n');

        return `用户搜索："${query}"

以下是部分可用的FAQ问题：
${questionSample}

请基于用户查询和可用问题，推荐最相关的3-5个问题ID，以JSON数组格式回复：
["Q001", "Q002", "Q003"]

只返回问题ID，确保ID在提供的列表中存在。`;
    }
    
    // 调用Gemini API
    async callGeminiAPI(prompt) {
        const url = `${this.config.endpoint}${this.config.model}:generateContent?key=${this.config.apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: this.config.temperature,
                maxOutputTokens: this.config.maxTokens,
                candidateCount: 1
            }
        };
        
        console.debug('Calling Gemini API:', url);
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            console.debug('Gemini API error response:', response.status, response.statusText);
            throw new Error(`Gemini API error: ${response.status}`);
        }
        
        const data = await response.json();
        console.debug('Gemini API response:', data);
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const text = data.candidates[0].content.parts[0].text;
            console.debug('Gemini response text:', text);
            try {
                const result = JSON.parse(text);
                console.debug('Parsed JSON result:', result);
                return result;
            } catch (e) {
                console.debug('Failed to parse as JSON, trying to extract JSON part');
                // 如果不是JSON，尝试提取JSON部分
                const jsonMatch = text.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const result = JSON.parse(jsonMatch[0]);
                    console.debug('Extracted JSON result:', result);
                    return result;
                }
                throw new Error('Invalid JSON response from Gemini');
            }
        }
        
        throw new Error('No valid response from Gemini');
    }
    


    // 生成基于FAQ知识库的AI回答
    async generateFAQBasedResponse(userMessage, relatedQuestions, language) {
        const prompt = this.buildChatPrompt(userMessage, relatedQuestions, language);

        try {
            const response = await this.callGeminiAPI(prompt);
            return this.parseChatResponse(response, relatedQuestions, language);
        } catch (error) {
            console.warn('AI回答生成失败:', error);
            // 回退到搜索结果格式
            return this.formatSearchResults(relatedQuestions, language);
        }
    }

    // 构建对话提示词
    buildChatPrompt(userMessage, relatedQuestions, language) {
        const langMap = {
            'zh': '中文',
            'en': 'English',
            'ms': 'Bahasa Malaysia'
        };

        const faqContext = relatedQuestions.map(q =>
            `问题: ${q.title[language]}\n答案: ${q.content[language]}\n编号: ${q.id}\n`
        ).join('\n---\n');

        // 获取对话上下文
        const chatContext = this.getChatContext(language);
        const contextSection = chatContext ? `\n对话历史:\n${chatContext}\n` : '';

        return `你是GoMyHire司机FAQ智能助手。${contextSection}

当前用户问题："${userMessage}"

以下是相关的FAQ知识库内容：
${faqContext}

请基于以上FAQ知识库内容和对话历史，用${langMap[language]}回答用户问题。要求：
1. 优先使用FAQ知识库中的准确信息
2. 考虑对话上下文，提供连贯的回答
3. 回答要专业、友好、有帮助
4. 如果涉及多个FAQ，可以综合回答
5. 在回答末尾提及相关的FAQ编号
6. 保持回答简洁明了，不超过200字

请直接回答，不要使用JSON格式。`;
    }

    // 解析对话响应
    parseChatResponse(response, relatedQuestions, language) {
        let content = '';

        if (typeof response === 'string') {
            content = response;
        } else if (response && response.candidates && response.candidates[0]) {
            content = response.candidates[0].content.parts[0].text;
        } else if (response && response.enhanced === false) {
            // API返回的未增强结果，回退到搜索结果
            return this.formatSearchResults(relatedQuestions, language);
        }

        if (content.trim()) {
            // 添加相关问题推荐
            if (relatedQuestions.length > 0) {
                content += `\n\n🔗 **相关FAQ编号**: `;
                content += relatedQuestions.slice(0, 3).map(q => q.id).join(', ');
            }
            return content;
        }

        // 如果AI回答失败，回退到搜索结果
        return this.formatSearchResults(relatedQuestions, language);
    }
    
    // 解析Gemini响应
    parseGeminiResponse(responseText, originalPrompt) {
        try {
            // 首先尝试直接解析为JSON
            const result = JSON.parse(responseText);
            if (result && result.keywords) {
                return {
                    enhanced: true,
                    originalQuery: this.extractQueryFromPrompt(originalPrompt),
                    enhancedQuery: result.keywords.join(' '),
                    keywords: result.keywords,
                    intent: result.intent,
                    suggestions: result.suggestions || []
                };
            }
        } catch (e) {
            // 如果不是纯JSON，尝试提取JSON部分
            try {
                const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/) || 
                                 responseText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const result = JSON.parse(jsonMatch[1] || jsonMatch[0]);
                    if (result && result.keywords) {
                        return {
                            enhanced: true,
                            originalQuery: this.extractQueryFromPrompt(originalPrompt),
                            enhancedQuery: result.keywords.join(' '),
                            keywords: result.keywords,
                            intent: result.intent,
                            suggestions: result.suggestions || []
                        };
                    }
                }
            } catch (e2) {
                console.warn('Failed to parse JSON from response:', responseText.substring(0, 100));
            }
        }
        
        return { enhanced: false, query: this.extractQueryFromPrompt(originalPrompt) };
    }
    
    // 从提示词中提取原始查询
    extractQueryFromPrompt(prompt) {
        const match = prompt.match(/："(.+?)"/);
        return match ? match[1] : '';
    }
    
    // 测试API连接
    async testAPIConnection() {
        try {
            const testPrompt = '请简单回复"连接成功"';
            const url = `${this.config.endpoint}${this.config.model}:generateContent?key=${this.config.apiKey}`;
            const requestBody = {
                contents: [{ parts: [{ text: testPrompt }] }],
                generationConfig: { temperature: 0.1, maxOutputTokens: 50 }
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }

            const data = await response.json();
            
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                const text = data.candidates[0].content.parts[0].text;
                if (text.includes('连接成功')) {
                    console.debug('API连接测试成功:', text);
                    return true;
                }
            }
            throw new Error('API response did not contain "连接成功"');

        } catch (error) {
            console.error('API连接测试失败:', error.message);
            return false;
        }
    }
    

    
    // 设置启用状态
    setEnabled(enabled) {
        this.enabled = enabled;
    }
    

}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeminiSearchAssistant;
} else {
    window.GeminiSearchAssistant = GeminiSearchAssistant;
}
