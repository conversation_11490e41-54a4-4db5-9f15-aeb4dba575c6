// 环境配置文件
(function() {
    'use strict';
    
    // 创建配置对象
    const appConfig = {
        // Gemini API 配置
        gemini: {
            apiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s', // 在这里替换为你的真实API Key
            model: 'gemini-2.5-flash-lite',
            endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/',
            enabled: true, // 默认启用
            temperature: 0.1,
            maxTokens: 1000
        },
        
        // 搜索配置
        search: {
            enableAI: true, // 默认启用AI搜索
            fallbackToTraditional: true, // 如果AI搜索失败，回退到传统搜索
            maxResults: 10
        }
    };

    // 浏览器环境下的配置处理
    if (typeof window !== 'undefined') {
        // 在客户端，我们从 localStorage 或默认值获取配置
        const savedConfig = localStorage.getItem('faq-config');
        if (savedConfig) {
            try {
                const parsed = JSON.parse(savedConfig);
                Object.assign(appConfig, parsed);
            } catch (e) {
                console.warn('Failed to parse saved config:', e);
            }
        }
        
        // 如果没有保存的API密钥，使用默认值（在生产环境中应该从安全的地方获取）
        if (!appConfig.gemini.apiKey || appConfig.gemini.apiKey.includes('XXXXX')) {
            // 这里应该从你的安全配置中获取API密钥
            // 例如从服务器端点获取，或者从环境变量注入的脚本标签中获取
            appConfig.gemini.apiKey = window.GEMINI_API_KEY || appConfig.gemini.apiKey;
        }
    }

    // 保存配置到 localStorage
    function saveConfig() {
        if (typeof window !== 'undefined') {
            localStorage.setItem('faq-config', JSON.stringify(appConfig));
        }
    }

    // 导出配置
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = appConfig;
    } else if (typeof window !== 'undefined') {
        window.CONFIG = appConfig;
        window.saveConfig = saveConfig;
    }

})();
