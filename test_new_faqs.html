<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增FAQ测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .faq-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .faq-title { font-weight: bold; color: #007bff; }
        .faq-id { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 新增FAQ功能测试</h1>
        <p>测试新添加的8个高优先级FAQ条目</p>

        <div>
            <button onclick="testNewFAQs()">测试新增FAQ</button>
            <button onclick="testSearchTags()">测试搜索标签</button>
            <button onclick="testRelatedQuestions()">测试相关问题链接</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div id="testResults"></div>
    </div>

    <!-- 引入必要的脚本文件 -->
    <script src="data.js"></script>
    <script src="i18n.js"></script>
    <script src="config.js"></script>

    <script>
        const testResults = document.getElementById('testResults');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            testResults.appendChild(div);
        }

        function clearResults() {
            testResults.innerHTML = '';
        }

        // 测试新增的FAQ
        function testNewFAQs() {
            log('🔍 开始测试新增的FAQ条目...', 'info');
            
            const newFAQIds = [
                'FC-ST-01', 'FC-GR-01', 'FC-AP-01', 'FC-AD-01',
                'FC-FL-01', 'FC-CH-01', 'FC-LC-01', 'FC-AR-01'
            ];

            let foundCount = 0;
            let missingFAQs = [];

            newFAQIds.forEach(id => {
                const faq = faqData.questions.find(q => q.id === id);
                if (faq) {
                    foundCount++;
                    log(`✅ 找到FAQ: ${id} - ${faq.title.zh}`, 'success');
                    
                    // 检查必要字段
                    if (!faq.category || !faq.priority || !faq.title || !faq.content || !faq.tags) {
                        log(`⚠️ FAQ ${id} 缺少必要字段`, 'error');
                    }
                    
                    // 检查多语言支持
                    if (!faq.title.zh || !faq.title.en || !faq.title.ms) {
                        log(`⚠️ FAQ ${id} 多语言标题不完整`, 'error');
                    }
                    
                    if (!faq.content.zh || !faq.content.en || !faq.content.ms) {
                        log(`⚠️ FAQ ${id} 多语言内容不完整`, 'error');
                    }
                } else {
                    missingFAQs.push(id);
                }
            });

            if (missingFAQs.length > 0) {
                log(`❌ 缺失的FAQ: ${missingFAQs.join(', ')}`, 'error');
            }

            log(`📊 测试结果: 找到 ${foundCount}/${newFAQIds.length} 个新FAQ`, foundCount === newFAQIds.length ? 'success' : 'error');
        }

        // 测试搜索标签
        function testSearchTags() {
            log('🔍 开始测试搜索标签...', 'info');
            
            const newFAQIds = [
                'FC-ST-01', 'FC-GR-01', 'FC-AP-01', 'FC-AD-01',
                'FC-FL-01', 'FC-CH-01', 'FC-LC-01', 'FC-AR-01'
            ];

            let taggedCount = 0;
            let missingTags = [];

            newFAQIds.forEach(id => {
                if (unifiedSearchTags[id]) {
                    taggedCount++;
                    const tags = unifiedSearchTags[id];
                    log(`✅ 搜索标签: ${id} - Primary: ${tags.primary.join(', ')}`, 'success');
                    
                    // 检查标签结构
                    if (!tags.primary || !tags.secondary || !tags.keywords || !tags.searchWeight) {
                        log(`⚠️ 搜索标签 ${id} 结构不完整`, 'error');
                    }
                } else {
                    missingTags.push(id);
                }
            });

            if (missingTags.length > 0) {
                log(`❌ 缺失搜索标签: ${missingTags.join(', ')}`, 'error');
            }

            log(`📊 搜索标签测试结果: ${taggedCount}/${newFAQIds.length} 个FAQ有搜索标签`, taggedCount === newFAQIds.length ? 'success' : 'error');
        }

        // 测试相关问题链接
        function testRelatedQuestions() {
            log('🔍 开始测试相关问题链接...', 'info');
            
            const newFAQIds = [
                'FC-ST-01', 'FC-GR-01', 'FC-AP-01', 'FC-AD-01',
                'FC-FL-01', 'FC-CH-01', 'FC-LC-01', 'FC-AR-01'
            ];

            const allFAQIds = new Set(faqData.questions.map(q => q.id));
            let validLinksCount = 0;
            let brokenLinks = [];

            newFAQIds.forEach(id => {
                const faq = faqData.questions.find(q => q.id === id);
                if (faq && faq.relatedQuestions) {
                    let validLinks = 0;
                    faq.relatedQuestions.forEach(relatedId => {
                        if (allFAQIds.has(relatedId)) {
                            validLinks++;
                        } else {
                            brokenLinks.push(`${id} → ${relatedId}`);
                        }
                    });
                    
                    if (validLinks === faq.relatedQuestions.length) {
                        validLinksCount++;
                        log(`✅ 相关问题链接: ${id} - ${validLinks}个有效链接`, 'success');
                    } else {
                        log(`⚠️ 相关问题链接: ${id} - ${validLinks}/${faq.relatedQuestions.length}个有效链接`, 'error');
                    }
                }
            });

            if (brokenLinks.length > 0) {
                log(`❌ 断链详情: ${brokenLinks.join(', ')}`, 'error');
            }

            log(`📊 相关问题链接测试结果: ${validLinksCount}/${newFAQIds.length} 个FAQ链接完全有效`, validLinksCount === newFAQIds.length ? 'success' : 'error');
        }

        // 页面加载时显示基本信息
        window.onload = function() {
            log('📋 新增FAQ测试页面已加载', 'info');
            log(`📊 当前FAQ总数: ${faqData.questions.length}`, 'info');
            log(`📊 搜索标签总数: ${Object.keys(unifiedSearchTags).length}`, 'info');
        };
    </script>
</body>
</html>
