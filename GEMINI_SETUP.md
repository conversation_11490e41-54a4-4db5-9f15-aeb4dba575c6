# FAQ 系统环境配置

## 设置 Gemini API Key

### 方法 1: 环境变量（推荐生产环境）
```bash
# Windows
set GEMINI_API_KEY=your_actual_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_actual_api_key_here
```

### 方法 2: 直接修改 config.js
打开 `config.js` 文件，将以下行：
```javascript
apiKey: process.env.GEMINI_API_KEY || 'AIzaSyBXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
```

替换为：
```javascript
apiKey: process.env.GEMINI_API_KEY || 'YOUR_ACTUAL_API_KEY_HERE',
```

### 方法 3: 通过全局变量注入（适用于托管环境）
在 `index.html` 的 `<head>` 部分添加：
```html
<script>
    window.GEMINI_API_KEY = 'YOUR_ACTUAL_API_KEY_HERE';
</script>
```

## 获取 Gemini API Key

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的 Google 账户
3. 创建新的 API Key
4. 复制生成的 API Key
5. 按照上述方法之一配置到项目中

## 功能说明

- **默认启用**: Gemini AI 助手默认启用，无需手动切换
- **流式响应**: 启用实时流式处理，提供即时反馈
- **智能搜索**: AI 会分析用户查询并提供相关关键词和建议
- **回退机制**: 如果 AI 搜索失败，会自动回退到传统搜索
- **搜索建议**: AI 会根据查询内容提供相关的搜索建议
- **实时指示器**: 搜索时显示流式处理进度

## 故障排除

如果 AI 助手不工作，请检查：
1. API Key 是否正确配置
2. 网络连接是否正常
3. 浏览器控制台是否有错误信息
4. API 配额是否还有剩余

点击搜索框中的 "AI" 按钮可以查看当前状态。
