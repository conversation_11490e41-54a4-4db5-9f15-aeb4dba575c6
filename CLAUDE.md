# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🎯 Project Overview

**GoMyHire Driver FAQ System** - A multilingual, mobile-first web application for driver support with AI-powered search capabilities. Built as a static site with no server dependencies.

**Primary Languages**: Chinese (zh), English (en), Malay (ms)  
**Target Users**: GoMyHire drivers seeking quick answers to common questions  
**Deployment**: Static hosting (GitHub Pages, Netlify, Vercel)

## 🚀 Quick Start (双击即用)

### 最简单的打开方式
直接 **双击 index.html** 即可在浏览器中打开，无需任何服务器或命令行操作。

### 开发测试方式
```bash
# 如果需要本地服务器测试
npx serve .                 # 或 python -m http.server 8000
# 然后访问 localhost:8000

# 检查浏览器控制台错误
F12 → Console 标签页

# 测试移动端响应式
F12 → Toggle Device Toolbar (Ctrl+Shift+M)
```

### File Structure
```
├── index.html              # Main entry point
├── app.js                  # Main application (FAQApp class)
├── data.js                 # FAQ content database (600KB+)
├── config.js               # Gemini API keys & settings
├── gemini-assistant.js     # AI search functionality
├── i18n.js                 # Multi-language support
├── styles.css              # Responsive mobile-first CSS
├── brand-theme.css         # GoMyHire brand styling
├── gomyhire-theme.css      # Additional theme variables
└── driverChats/            # Raw driver chat logs (reference data)
```

## 🏗️ Architecture Overview

### Core Classes
- **FAQApp**: Main application orchestrator in app.js
- **I18nManager**: Multi-language support in i18n.js  
- **DataManager**: (In data.js) - Manages FAQ content, categories, search
- **GeminiSearchAssistant**: AI-powered search enhancement

### Data Structure
```javascript
// Categories (unified system)
{
  technical: {
    id: "technical",
    icon: "📱",
    priority: 1,
    name: { zh: "技术问题", en: "Technical Issues", ms: "Isu Teknikal" },
    legacyIds: ["app-usage", "technical"]
  }
}

// Questions
{
  id: "FC-RG-01",
  category: "technical",
  priority: "high",
  title: { zh: "...", en: "...", ms: "..." },
  content: { zh: "...", en: "...", ms: "..." },
  tags: ["标签1", "tag1"],
  relatedQuestions: ["FC-RG-02"]
}
```

### Search System
1. **Traditional Search**: Exact match on ID, keywords, tags (DataManager)
2. **AI Enhancement**: Gemini API for query expansion and suggestions
3. **Real-time**: 500ms debounced search on input
4. **Multilingual**: Search in current language context

## 🔧 Configuration & API Keys

### Gemini AI Setup
- **File**: config.js
- **Key Location**: config.gemini.apiKey (line 9)
- **Current**: Uses placeholder "AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s"
- **Model**: gemini-2.5-flash-lite
- **Features**: Query enhancement, search suggestions

**Important**: Replace API key before production use

### Language Settings
- **Default**: Chinese (zh)
- **Storage**: localStorage.getItem('language')
- **Switching**: Top-right language buttons trigger i18n.setLanguage()

## 📱 Mobile-First Design

### Responsive Breakpoints
- **Mobile**: < 768px (sidebar overlay, compact layout)
- **Tablet**: 768px - 1024px (280px sidebar)
- **Desktop**: > 1024px (260px fixed sidebar)

### Key Mobile Optimizations
- Touch targets: 44px minimum (iOS guideline)
- Safe area insets for iPhone X+
- Virtual keyboard adaptation
- Haptic feedback on mobile
- Smooth scroll restoration

## 🔍 Content Management

### FAQ Categories (Priority Order)
1. **Technical** (📱) - APP issues, login, sync
2. **Financial** (💰) - Payments, withdrawals, income
3. **Service** (🛎️) - Paging, customer communication
4. **Registration** (🚗) - Driver onboarding
5. **Communication** (💬) - Customer interactions, ratings
6. **Emergency** (🚨) - Critical situations

### Adding New Questions
1. Open data.js
2. Add to `questions` array with multilingual content
3. Use category IDs from unifiedCategorySystem
4. Include related question IDs for cross-linking
5. Test search functionality for new content

### Content Sources
- **Primary**: Structured FAQ data in data.js
- **Reference**: Raw chat logs in driverChats/ (for research)
- **Updates**: Direct modification of data.js required

## 🧪 Testing Checklist

### Core Functionality
- [ ] Language switching works across all content
- [ ] Search returns relevant results
- [ ] Category navigation loads correctly
- [ ] Mobile menu toggles properly
- [ ] Back/forward browser navigation
- [ ] URL parameters (q=, c=, s=) work

### AI Features (if API key provided)
- [ ] Gemini search enhancement
- [ ] Search suggestions appear
- [ ] API connection test passes
- [ ] Fallback to traditional search on failure

### Responsive Testing
- [ ] iPhone SE (375px width)
- [ ] iPad (768px width)  
- [ ] Desktop (1024px+ width)
- [ ] Portrait/landscape orientation changes

## 🚨 Common Issues & Fixes

### Search Not Working
1. Check browser console for errors
2. Verify data.js loaded successfully
3. Test with exact question ID (e.g., "FC-RG-01")

### Language Not Switching
1. Check localStorage for 'language' key
2. Verify i18n.js loaded before app.js
3. Test in incognito mode

### Mobile Menu Issues
1. Check viewport meta tag in index.html
2. Test hamburger menu click handler
3. Verify sidebar overlay z-index

### Gemini API Errors
1. Check network tab for API calls
2. Verify API key format (should not contain XXXXX)
3. Test with curl: `curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key=YOUR_KEY"`