// GoMyHire司机FAQ系统主应用
class FAQApp {
    constructor() {
        this.i18n = new I18nManager();
        this.dataManager = new DataManager();

        // 初始化数据管理器（重要：确保统一分类系统正常工作）
        this.dataManager.initialize();

        // 调试信息：验证分类系统是否正常工作
        console.log('📋 DataManager初始化完成');
        const categories = this.dataManager.getCategories();
        console.log('📊 可用分类数量:', Object.keys(categories).length);
        console.log('📊 分类列表:', Object.keys(categories));

        // 验证分类系统完整性
        if (this.dataManager.validateCategorySystem) {
            const validation = this.dataManager.validateCategorySystem();
            if (!validation.success) {
                console.warn('⚠️ 分类系统验证失败:', validation.issues);
            } else {
                console.log('✅ 分类系统验证通过');
            }
        }
        
        // 安全地初始化 Gemini 助手
        try {
            if (window.CONFIG && window.CONFIG.gemini) {
                this.geminiAssistant = new GeminiSearchAssistant(window.CONFIG);
                this.geminiEnabled = true;

                // 设置DataManager引用，让Gemini助手能够访问FAQ数据
                this.geminiAssistant.setDataManager(this.dataManager);

                // 测试API连接
                this.geminiAssistant.testAPIConnection().then(success => {
                    if (success) {
                        console.log('✅ Gemini API 连接成功');
                    } else {
                        console.warn('⚠️ Gemini API 连接失败，将使用传统搜索');
                        this.geminiEnabled = false;
                    }
                }).catch(error => {
                    console.warn('⚠️ Gemini API 测试出错:', error);
                    this.geminiEnabled = false;
                });
            } else {
                console.warn('CONFIG not available, Gemini assistant disabled');
                this.geminiAssistant = null;
                this.geminiEnabled = false;
            }
        } catch (error) {
            console.error('Failed to initialize Gemini assistant:', error);
            this.geminiAssistant = null;
            this.geminiEnabled = false;
        }
        
        this.currentPage = 'welcome';
        this.currentQuestion = null;
        this.searchTimeout = null; // 用于防抖搜索
        this.currentTheme = localStorage.getItem('theme') || 'light';

        // 初始化主题
        this.initTheme();

        this.init();
    }
    
    // 初始化应用
    init() {
        try {
            console.log('🚀 开始初始化FAQ应用...');

            this.setupEventListeners();
            console.log('✅ 事件监听器设置完成');

            // 不在初始化时渲染分类，而是在用户点击时动态创建
            // this.renderCategories();
            // console.log('✅ 分类导航渲染完成');

            this.renderQuickStartButtons();
            console.log('✅ 快速开始按钮渲染完成');

            this.showWelcomePage();
            console.log('✅ 欢迎页面显示完成');

            this.setupBackToTop();
            console.log('✅ 返回顶部按钮设置完成');

            this.i18n.updatePageTexts();
            console.log('✅ 国际化文本更新完成');

            // 处理URL参数
            this.handleUrlParams();
            console.log('✅ URL参数处理完成');

            console.log('🎉 FAQ应用初始化成功！');

            // 隐藏加载提示
            this.hideLoadingIndicator();

        } catch (error) {
            console.error('❌ FAQ应用初始化失败:', error);
            console.error('错误堆栈:', error.stack);

            // 显示用户友好的错误信息
            this.showErrorMessage('应用初始化失败，请刷新页面重试。');
        }
    }

    // 显示错误信息
    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            z-index: 10000;
            max-width: 400px;
            text-align: center;
            font-family: Arial, sans-serif;
        `;
        errorDiv.innerHTML = `
            <h3>⚠️ 系统错误</h3>
            <p>${message}</p>
            <button onclick="location.reload()" style="
                background: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">刷新页面</button>
        `;
        document.body.appendChild(errorDiv);
    }
    
    // 设置事件监听器 - 移动端优化版本
    setupEventListeners() {
        // 移动端底部导航
        this.setupBottomNavigation();
        
        // 浮动对话按钮 (FAB) - 移动端专用
        this.setupFABButtons();

        // 触摸反馈 - 移动端优化
        this.setupTouchFeedback();

        // 搜索功能 - 移动端优化
        const searchInput = document.getElementById('searchInput');
        const geminiToggle = document.getElementById('geminiToggle');
        
        // 实时搜索 - 输入时触发（移动端优化）
        searchInput.addEventListener('input', (e) => {
            this.performRealtimeSearch(e.target.value);
        });
        
        // 回车键也触发搜索
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = null;
                }
                this.performSearch(e.target.value);
            }
        });
        
        // Gemini状态按钮
        geminiToggle.addEventListener('click', (e) => {
            e.preventDefault();
            this.showGeminiStatus();
        });



        // 主题切换按钮
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // 语言切换按钮
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = btn.getAttribute('data-lang');
                this.switchLanguage(lang);
            });
        });
    }
    
    // 初始化主题
    initTheme() {
        // 应用保存的主题
        this.applyTheme(this.currentTheme);

        // 更新主题切换按钮图标
        this.updateThemeToggleIcon();
    }

    // 切换主题
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.currentTheme);
        this.applyTheme(this.currentTheme);
        this.updateThemeToggleIcon();

        // 添加切换动画效果
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    // 应用主题
    applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark-theme');
        } else {
            document.documentElement.classList.remove('dark-theme');
        }
    }

    // 更新主题切换按钮图标
    updateThemeToggleIcon() {
        const themeIcon = document.querySelector('.theme-icon');
        if (themeIcon) {
            themeIcon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
        }

        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.title = this.currentTheme === 'light' ?
                this.i18n.t('switchToDarkTheme') :
                this.i18n.t('switchToLightTheme');
        }
    }

    // 切换语言
    switchLanguage(lang) {
        // 更新按钮状态
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-lang="${lang}"]`).classList.add('active');

        // 设置语言
        this.i18n.setLanguage(lang);

        // 重新渲染当前页面
        this.refreshCurrentPage();
        this.renderQuickStartButtons(); // 重新渲染快速访问按钮以更新语言
    }
    
    // 实时搜索（防抖）
    performRealtimeSearch(query) {
        // 清除之前的定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        // 如果查询为空，显示欢迎页面
        if (!query || query.trim().length === 0) {
            this.showWelcomePage();
            return;
        }
        
        // 设置防抖延迟（500ms）
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query.trim());
        }, 500);
    }
    
    // 执行搜索
    async performSearch(query = null, showLoading = false) {
        if (!query) {
            query = document.getElementById('searchInput').value.trim();
        }
        if (!query) {
            this.showWelcomePage();
            return;
        }
        
        // 只在明确指定时显示加载指示器
        if (showLoading) {
            this.showLoadingOverlay(true);
            this.showStreamingIndicator(true);
        }
        
        try {
            let results;
            const language = this.i18n.getCurrentLanguage();
            
            // 尝试使用 Gemini 增强搜索（静默模式，不显示错误）
            if (this.geminiEnabled && this.geminiAssistant && this.geminiAssistant.isAvailable()) {
                try {
                    const enhanced = await this.geminiAssistant.enhanceSearchQuery(query, language);

                    if (enhanced.enhanced) {
                        // 使用增强的查询进行搜索
                        results = await this.dataManager.searchQuestions(enhanced.enhancedQuery, language);

                        // 如果增强搜索结果不够好，回退到原始查询
                        if (results.length < 2) {
                            results = await this.dataManager.searchQuestions(query, language);
                        }

                        // 显示搜索增强信息
                        if (enhanced.suggestions && enhanced.suggestions.length > 0) {
                            this.showSearchEnhancement(enhanced);
                        }
                    } else {
                        results = await this.dataManager.searchQuestions(query, language);
                    }
                } catch (error) {
                    // 静默处理 Gemini 错误，不显示警告
                    results = await this.dataManager.searchQuestions(query, language);
                }
            } else {
                results = await this.dataManager.searchQuestions(query, language);
            }
            
            this.showSearchResults(query, results);
        } catch (error) {
            console.error('Search failed:', error);
            if (showLoading) {
                this.showNotification('搜索出现错误，请重试');
            }
        } finally {
            if (showLoading) {
                this.showLoadingOverlay(false);
                this.showStreamingIndicator(false);
            }
        }
    }
    
    // 显示搜索结果
    showSearchResults(query, results) {
        this.currentPage = 'search';
        this.hideAllPages();

        const searchPage = document.getElementById('searchPage');
        const searchResults = document.getElementById('searchResults');

        // 渲染搜索结果，包含搜索信息
        const count = results.length;

        if (results.length === 0) {
            searchResults.innerHTML = `
                <div class="search-header">
                    <h2>${this.i18n.t('searchResultsTitle')}</h2>
                    <p>${this.i18n.t('searchFor')} "${query}" ${this.i18n.t('foundResults', { count })}</p>
                </div>
                <div class="no-results">
                    <h3>${this.i18n.t('noResultsFound')}</h3>
                    <p>${this.i18n.t('noResultsDesc')}</p>
                    <button onclick="app.showWelcomePage()" class="back-to-home-btn">${this.i18n.t('backToHomeBtn')}</button>
                </div>
            `;
        } else {
            searchResults.innerHTML = `
                <div class="search-header">
                    <h2>${this.i18n.t('searchResultsTitle')}</h2>
                    <p>${this.i18n.t('searchFor')} "${query}" ${this.i18n.t('foundResults', { count })}</p>
                </div>
                <div class="search-results-list">
                    ${results.map(result => this.renderSearchResultItem(result)).join('')}
                </div>
            `;

            // 添加点击事件
            searchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', () => {
                    const questionId = item.dataset.questionId;
                    this.showQuestion(questionId);
                });
            });
        }

        searchPage.classList.remove('page-hidden');
    }
    
    // 渲染搜索结果项
    renderSearchResultItem(question) {
        // 直接使用question对象，因为searchQuestions返回的是问题数组，不是result对象
        const lang = this.i18n.getCurrentLanguage();
        const categories = this.dataManager.getCategories();
        const category = categories[question.category];

        // 安全获取分类名称
        let categoryName = this.i18n.t('unknownCategory');
        if (category) {
            categoryName = category.name[lang];
        } else {
            // 尝试使用CategoryAdapter获取分类信息
            const categoryInfo = this.dataManager.getCategoryInfo ?
                this.dataManager.getCategoryInfo(question.category, lang) : null;
            if (categoryInfo) {
                categoryName = categoryInfo.name;
            }
        }

        // 生成摘要
        const excerpt = this.generateExcerpt(question.content[lang], 150);

        return `
            <div class="search-result-item" data-question-id="${question.id}">
                <div class="search-result-header">
                    <span class="search-result-id">${question.id}</span>
                    <h3 class="search-result-title">${question.title[lang]}</h3>
                    <span class="search-result-category">${categoryName}</span>
                </div>
                <div class="search-result-excerpt">${excerpt}</div>
            </div>
        `;
    }
    
    // 生成内容摘要
    generateExcerpt(content, maxLength) {
        const text = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    // 渲染分类导航
    renderCategories() {
        console.log(' 🎨 开始渲染分类按钮...');
        
        // 检查是否存在 categories-container，如果不存在则动态创建
        let categoriesContainer = document.getElementById('categories-container');
        
        if (!categoriesContainer) {
            console.warn('⚠️ categories-container 不存在，动态创建中...');
            
            // 创建分类页面容器
            let categoriesPage = document.getElementById('categoriesPage');
            if (!categoriesPage) {
                categoriesPage = document.createElement('div');
                categoriesPage.id = 'categoriesPage';
                categoriesPage.className = 'categories-page page-hidden';
                
                // 添加分类页面标题
                const pageTitle = document.createElement('div');
                pageTitle.className = 'page-title';
                pageTitle.innerHTML = '<h2>选择分类</h2>';
                
                // 创建分类容器
                categoriesContainer = document.createElement('div');
                categoriesContainer.id = 'categories-container';
                categoriesContainer.className = 'categories-container';
                
                // 组装分类页面
                categoriesPage.appendChild(pageTitle);
                categoriesPage.appendChild(categoriesContainer);
                
                // 插入到主内容区域
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.appendChild(categoriesPage);
                    console.log('✅ 动态创建了分类页面和容器');
                } else {
                    console.error('❌ 无法找到main-content容器');
                    return;
                }
            } else {
                // 分类页面存在但容器不存在
                categoriesContainer = document.createElement('div');
                categoriesContainer.id = 'categories-container';
                categoriesContainer.className = 'categories-container';
                categoriesPage.appendChild(categoriesContainer);
            }
        }
        
        const categories = this.dataManager.getCategories();
        const lang = this.i18n.getCurrentLanguage();
        
        // 清空容器内容
        categoriesContainer.innerHTML = '';
        
        // 渲染分类按钮
        const categoryItems = Object.values(categories)
            .sort((a, b) => a.priority - b.priority)
            .map(category => {
                const questionCount = this.dataManager.getQuestionsByCategory(category.id).length;
                return `
                    <button class="category-btn" data-category="${category.id}">
                        <span class="category-icon">${category.icon}</span>
                        <span class="category-name">${category.name[lang]}</span>
                        <span class="category-count">${questionCount}</span>
                    </button>
                `;
            }).join('');
        
        categoriesContainer.innerHTML = categoryItems;
        
        // 添加点击事件
        categoriesContainer.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const categoryId = e.currentTarget.dataset.category;
                this.showCategoryQuestions(categoryId);
            });
        });
        
        console.log(`✅ 渲染了 ${Object.keys(categories).length} 个分类按钮`);
    }
    
    // 动态生成快速开始按钮
    renderQuickStartButtons() {
        // 使用faqCards容器而不是不存在的quick-start-buttons-container
        const container = document.getElementById('faqCards');
        if (!container) {
            console.warn('⚠️ faqCards容器不存在');
            return;
        }

        const categories = this.dataManager.getCategories();
        const quickStartCategories = Object.values(categories)
            .sort((a, b) => a.priority - b.priority);

        // 创建欢迎页面内容
        const lang = this.i18n.getCurrentLanguage();
        container.innerHTML = `
            <div class="welcome-content">
                <div class="welcome-header">
                    <h2>${this.i18n.t('welcomeFAQTitle')}</h2>
                    <p>${this.i18n.t('welcomeFAQDesc')}</p>
                </div>
                <div class="category-grid">
                    ${quickStartCategories.map(category => `
                        <button class="category-card" data-category="${category.id}" type="button">
                            <span class="category-icon">${category.icon}</span>
                            <span class="category-name" data-i18n-category-name="${category.id}">${category.name[lang]}</span>
                            <span class="category-count">${this.dataManager.getQuestionsByCategory(category.id).length} ${this.i18n.t('questionsCount')}</span>
                        </button>
                    `).join('')}
                </div>
            </div>
        `;

        // 为新生成的按钮添加事件监听器
        container.querySelectorAll('.category-card').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const categoryId = e.currentTarget.dataset.category;
                this.showCategoryPage(categoryId);
            });
        });

        console.log(`✅ 渲染了 ${quickStartCategories.length} 个分类卡片到欢迎页面`);
    }
    
    // 显示分类页面 - 重定向到showCategoryQuestions方法
    showCategoryPage(categoryId) {
        console.log(`🔍 重定向到分类问题页面: ${categoryId}`);
        this.showCategoryQuestions(categoryId);
    }
    
    // 渲染问题项
    renderQuestionItem(question) {
        const lang = this.i18n.getCurrentLanguage();
        const summary = this.generateExcerpt(question.content[lang], 100);
        
        return `
            <div class="question-item" data-question-id="${question.id}">
                <div class="question-header">
                    <span class="question-id">${question.id}</span>
                    <h3 class="question-title">${question.title[lang]}</h3>
                    <span class="question-priority priority-${question.priority}">
                        ${this.i18n.t('priority' + question.priority.charAt(0).toUpperCase() + question.priority.slice(1))}
                    </span>
                </div>
                <div class="question-summary">${summary}</div>
            </div>
        `;
    }
    
    // 显示问题详情
    showQuestion(questionId) {
        // 自动滚动到页面顶部
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });

        const question = this.dataManager.getQuestionById(questionId);
        if (!question) {
            console.warn(`⚠️ 问题不存在: ${questionId}`);
            return;
        }

        this.currentQuestion = question;
        this.currentPage = 'faq';
        this.hideAllPages();

        const faqPage = document.getElementById('faqPage');
        const faqContent = document.getElementById('faqContent');

        const lang = this.i18n.getCurrentLanguage();
        const categories = this.dataManager.getCategories();
        const category = categories[question.category];

        // 安全检查：如果分类不存在，尝试获取分类信息
        let categoryName = '未知分类';
        let categoryId = question.category;

        if (category) {
            categoryName = category.name[lang];
        } else {
            // 尝试使用CategoryAdapter获取分类信息
            const categoryInfo = this.dataManager.getCategoryInfo ?
                this.dataManager.getCategoryInfo(question.category, lang) : null;
            if (categoryInfo) {
                categoryName = categoryInfo.name;
                categoryId = categoryInfo.id;
            }
        }

        // 获取相关问题
        const relatedQuestions = this.dataManager.getRelatedQuestions(question.id);

        // 更新FAQ内容，包含面包屑和相关问题
        faqContent.innerHTML = `
            <div class="breadcrumb">
                <button onclick="app.showWelcomePage()" class="breadcrumb-link">首页</button>
                <span class="breadcrumb-separator">></span>
                <button onclick="app.showCategoryPage('${categoryId}')" class="breadcrumb-link">${categoryName}</button>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-current">${question.title[lang]}</span>
            </div>
            <div class="faq-header">
                <span class="faq-id">${question.id}</span>
                <h1 class="faq-title">
                    ${question.title[lang]}
                    <span class="faq-priority priority-${question.priority}">
                        ${question.priority}
                    </span>
                </h1>
            </div>
            <div class="faq-body">
                ${question.content[lang]}
            </div>
            ${relatedQuestions.length > 0 ? `
                <div class="related-questions">
                    <h3>${this.i18n.t('relatedQuestionsTitle')}</h3>
                    <div class="related-list">
                        ${relatedQuestions.map(rq => `
                            <div class="related-item" onclick="app.showQuestion('${rq.id}')">
                                <span class="related-title">${rq.title[lang]}</span>
                                <span class="related-arrow">→</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        `;

        faqPage.classList.remove('page-hidden');
    }
    
    // 渲染相关问题
    renderRelatedQuestions(container, question) {
        const relatedQuestions = this.dataManager.getRelatedQuestions(question.id);
        const lang = this.i18n.getCurrentLanguage();
        
        if (relatedQuestions.length === 0) {
            container.classList.add('page-hidden');
            return;
        }
        
        container.innerHTML = `
            <h3>${this.i18n.t('relatedQuestions')}</h3>
            <div class="related-list">
                ${relatedQuestions.map(q => `
                    <a href="#" class="related-item" data-question-id="${q.id}">
                        <span class="related-item-id">${q.id}</span>
                        <span class="related-item-title">${q.title[lang]}</span>
                    </a>
                `).join('')}
            </div>
        `;
        
        // 添加点击事件
        container.querySelectorAll('.related-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const questionId = e.currentTarget.dataset.questionId;
                this.showQuestion(questionId);
            });
        });
        
        container.classList.remove('page-hidden');
    }
    
    // 显示欢迎页面
    showWelcomePage() {
        this.currentPage = 'welcome';
        this.hideAllPages();
        document.getElementById('welcomePage').classList.remove('page-hidden');
    }

    // 隐藏所有页面
    hideAllPages() {
        const pages = ['welcomePage', 'faqPage', 'searchPage', 'categoryPage', 'categoriesPage', 'categoryQuestionsPage'];
        pages.forEach(pageId => {
            const page = document.getElementById(pageId);
            if (page) {
                page.classList.add('page-hidden');
            }
        });
        
        // 隐藏聊天容器
        const chatContainer = document.getElementById('chatContainer');
        if (chatContainer) {
            chatContainer.remove();
        }
    }
    
    // 显示分类页面
    showCategoriesPage() {
        this.currentPage = 'categories';
        this.hideAllPages();
        
        // 确保分类已渲染
        if (!document.getElementById('categories-container')) {
            this.renderCategories();
        }
        
        const categoriesPage = document.getElementById('categoriesPage');
        if (categoriesPage) {
            categoriesPage.classList.remove('page-hidden');
            console.log('📂 显示分类页面');
        } else {
            console.error('❌ 分类页面不存在');
        }
    }
    
    // 显示分类问题页面
    showCategoryQuestions(categoryId) {
        console.log(`🔍 显示分类 ${categoryId} 的问题`);
        
        this.currentPage = 'categoryQuestions';
        this.hideAllPages();
        
        const questions = this.dataManager.getQuestionsByCategory(categoryId);
        const categories = this.dataManager.getCategories();
        const category = categories[categoryId];
        const lang = this.i18n.getCurrentLanguage();
        
        // 安全获取分类信息
        let categoryName = this.i18n.t('unknownCategory');
        let categoryIcon = '📋';

        if (category) {
            categoryName = category.name[lang];
            categoryIcon = category.icon || '📋';
        }
        
        // 创建分类问题页面
        let categoryQuestionsPage = document.getElementById('categoryQuestionsPage');
        if (!categoryQuestionsPage) {
            categoryQuestionsPage = document.createElement('div');
            categoryQuestionsPage.id = 'categoryQuestionsPage';
            categoryQuestionsPage.className = 'category-questions-page page-hidden';
            
            const mainContent = document.querySelector('.main-content');
            mainContent.appendChild(categoryQuestionsPage);
        }
        
        // 渲染分类问题内容
        categoryQuestionsPage.innerHTML = `
            <div class="page-header">
                <button class="back-btn" onclick="app.showWelcomePage()">${this.i18n.t('backToHomePage')}</button>
                <h2>${categoryIcon} ${categoryName}</h2>
                <p class="category-count">${this.i18n.t('totalQuestions', { count: questions.length })}</p>
            </div>
            <div class="questions-list">
                ${questions.map(q => `
                    <div class="question-item" onclick="app.showQuestion('${q.id}')">
                        <div class="question-text">${q.title[lang]}</div>
                        <div class="question-arrow">→</div>
                    </div>
                `).join('')}
            </div>
        `;
        
        categoryQuestionsPage.classList.remove('page-hidden');
    }
    
    // 刷新当前页面
    refreshCurrentPage() {
        this.renderCategories();
        
        switch (this.currentPage) {
            case 'welcome':
                this.showWelcomePage();
                break;
            case 'faq':
                if (this.currentQuestion) {
                    this.showQuestion(this.currentQuestion.id);
                }
                break;
            case 'search':
                // 重新执行搜索（显示加载指示器）
                this.performSearch(null, true);
                break;
            case 'category':
                // 需要保存当前分类ID
                break;
        }
    }
    
    // 处理URL参数
    handleUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const questionId = urlParams.get('q');
        const category = urlParams.get('c');
        const search = urlParams.get('s');

        if (questionId) {
            this.showQuestion(questionId);
        } else if (category) {
            this.showCategoryPage(category);
        } else if (search) {
            document.getElementById('searchInput').value = search;
            this.performSearch(null, true);
        }
    }

    // 移动端底部导航设置 - 替换原来的侧边栏功能
    setupBottomNavigation() {
        const bottomNav = document.querySelector('.bottom-nav');
        if (!bottomNav) return;

        // 底部导航点击事件
        bottomNav.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = item.dataset.tab;
                this.switchBottomNavTab(tab);
                
                // 触摸反馈
                if (navigator.vibrate) {
                    navigator.vibrate(30);
                }
            });
        });
    }

    // 切换底部导航标签
    switchBottomNavTab(tab) {
        // 更新活动状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // 根据标签切换功能
        switch(tab) {
            case 'faq':
                this.showWelcomePage();
                break;
            case 'chat':
                this.toggleChatMode();
                break;
        }
    }

    // 触摸反馈设置
    setupTouchFeedback() {
        // 为所有可点击元素添加触摸反馈
        const clickableElements = document.querySelectorAll(`
            button,
            .btn,
            .category-link,
            .lang-btn,
            .search-result-item,
            .quick-start-buttons button,
            [role="button"]
        `);

        clickableElements.forEach(element => {
            // 触摸开始
            element.addEventListener('touchstart', (e) => {
                element.classList.add('touch-active');

                // 轻微震动反馈
                if (navigator.vibrate) {
                    navigator.vibrate(10);
                }
            }, { passive: true });

            // 触摸结束
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.classList.remove('touch-active');
                }, 150);
            }, { passive: true });

            // 触摸取消
            element.addEventListener('touchcancel', (e) => {
                element.classList.remove('touch-active');
            }, { passive: true });
        });

        // 为搜索输入框添加虚拟键盘适配
        this.setupKeyboardAdaptation();
    }

    // 虚拟键盘适配
    setupKeyboardAdaptation() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;

        let initialViewportHeight = window.innerHeight;

        // 监听视口高度变化（虚拟键盘弹出/收起）
        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDiff = initialViewportHeight - currentHeight;

            if (heightDiff > 150) {
                // 虚拟键盘弹出
                document.body.classList.add('keyboard-open');

                // 确保搜索框可见
                setTimeout(() => {
                    searchInput.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 300);
            } else {
                // 虚拟键盘收起
                document.body.classList.remove('keyboard-open');
            }
        });

        // 输入框获得焦点时的处理
        searchInput.addEventListener('focus', () => {
            setTimeout(() => {
                searchInput.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }, 300);
        });
    }

    // 显示加载遮罩
    showLoadingOverlay(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.add('show');
        } else {
            overlay.classList.remove('show');
        }
    }

    // 隐藏底部加载提示
    hideLoadingIndicator() {
        // 查找所有可能的加载提示元素
        const loadingElements = document.querySelectorAll('p');
        loadingElements.forEach(element => {
            const text = element.textContent.trim();
            if (text === '加载中...' || text === 'Loading...' || text === 'Memuatkan...') {
                element.style.display = 'none';
            }
        });
    }
    
    // 设置FAB按钮
    setupFABButtons() {
        const fabChat = document.getElementById('fabChat');

        if (fabChat) {
            fabChat.addEventListener('click', () => {
                this.toggleChatMode();
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            });
        }
    }

    // 切换聊天模式
    toggleChatMode() {
        if (this.currentPage === 'chat') {
            this.showWelcomePage();
            return;
        }
        
        this.currentPage = 'chat';
        this.hideAllPages();
        this.showChatInterface();
        
        // 更新底部导航
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector('[data-tab="chat"]').classList.add('active');
    }

    // 显示聊天界面
    showChatInterface() {
        const chatContainer = document.createElement('div');
        chatContainer.id = 'chatContainer';
        chatContainer.className = 'chat-container';
        chatContainer.innerHTML = `
            <div class="chat-header">
                <button class="chat-back" onclick="app.closeChat()">←</button>
                <h2>💬 AI助手</h2>
                <button class="close-chat" onclick="app.closeChat()">×</button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message bot-message">
                    <div class="message-content">
                        👋 你好！我是GoMyHire AI助手，有什么可以帮到你的吗？
                    </div>
                </div>
            </div>
            <div class="chat-input-container">
                <input type="text" id="chatInput" placeholder="输入你的问题..." class="chat-input">
                <button class="send-btn" onclick="app.sendChatMessage()">
                    <span class="icon">📤</span>
                </button>
            </div>
        `;

        // 清除之前可能存在的聊天容器
        const existingChat = document.getElementById('chatContainer');
        if (existingChat) {
            existingChat.remove();
        }

        // 找到主内容容器并添加聊天界面
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.appendChild(chatContainer);
        } else {
            console.error('主内容容器未找到');
            return;
        }

        // 添加active类以显示聊天界面
        chatContainer.classList.add('active');

        // 设置聊天输入框事件
        const chatInput = document.getElementById('chatInput');
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });

        chatInput.focus();
    }

    // 发送聊天消息
    async sendChatMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();
        
        if (!message) return;
        
        // 添加用户消息
        this.addChatMessage(message, 'user');
        chatInput.value = '';
        
        // 添加等待指示器
        this.addChatMessage('...', 'bot', 'typing');
        
        try {
            if (this.geminiEnabled && this.geminiAssistant) {
                const response = await this.geminiAssistant.chat(message, this.i18n.getCurrentLanguage());
                this.replaceTypingMessage(response);
            } else {
                // 简单的FAQ搜索回复
                const results = await this.dataManager.searchQuestions(message, this.i18n.getCurrentLanguage());
                if (results.length > 0) {
                    const reply = this.generateSimpleReply(results);
                    this.replaceTypingMessage(reply);
                } else {
                    this.replaceTypingMessage('抱歉，没有找到相关答案。请尝试其他关键词，或联系客服。');
                }
            }
        } catch (error) {
            console.error('聊天回复失败:', error);
            this.replaceTypingMessage('抱歉，暂时无法回复。请稍后再试。');
        }
    }

    // 添加聊天消息
    addChatMessage(content, type, className = '') {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message ${className}`;
        messageDiv.innerHTML = `
            <div class="message-content">${content}</div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        return messageDiv;
    }

    // 替换输入中的消息
    replaceTypingMessage(content) {
        const typingMessages = document.querySelectorAll('.typing');
        typingMessages.forEach(msg => msg.remove());
        this.addChatMessage(content, 'bot');
    }

    // 生成简单回复
    generateSimpleReply(results) {
        const topResults = results.slice(0, 3);
        let reply = '我找到了一些相关信息：\n\n';
        
        topResults.forEach((result, index) => {
            const lang = this.i18n.getCurrentLanguage();
            const question = result.question;
            reply += `${index + 1}. **${question.title[lang]}**\n点击查看详情\n\n`;
        });
        
        return reply;
    }

    // 关闭聊天
    closeChat() {
        const chatContainer = document.getElementById('chatContainer');
        if (chatContainer) {
            chatContainer.remove();
        }

        // 清除对话历史
        if (this.geminiAssistant && this.geminiAssistant.clearChatHistory) {
            this.geminiAssistant.clearChatHistory();
        }

        // 更新底部导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector('[data-tab="faq"]').classList.add('active');

        this.showWelcomePage();
    }

    // 处理紧急联系


    // 设置返回顶部按钮
    setupBackToTop() {
        const backToTopBtn = document.getElementById('backToTopBtn');
        if (!backToTopBtn) {
            // 创建返回顶部按钮
            const btn = document.createElement('button');
            btn.id = 'backToTopBtn';
            btn.className = 'back-to-top';
            btn.innerHTML = '↑';
            document.body.appendChild(btn);
            
            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    btn.classList.add('show');
                } else {
                    btn.classList.remove('show');
                }
            });
            
            btn.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
                if (navigator.vibrate) {
                    navigator.vibrate(20);
                }
            });
            return;
        }

        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // 显示搜索增强建议
    showSearchEnhancement(enhanced) {
        const enhancementInfo = document.createElement('div');
        enhancementInfo.className = 'search-enhancement-info';
        enhancementInfo.innerHTML = `
            <div class="enhancement-header">
                <span class="gemini-icon">✨</span>
                <span>AI 搜索助手建议</span>
            </div>
            <div class="enhancement-suggestions">
                ${enhanced.suggestions.map(suggestion => 
                    `<button class="suggestion-btn" onclick="app.searchSuggestion('${suggestion}')">${suggestion}</button>`
                ).join('')}
            </div>
        `;
        
        // 插入到搜索结果页面
        const searchPage = document.getElementById('searchPage');
        const existingInfo = searchPage.querySelector('.search-enhancement-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        const searchInfo = document.getElementById('searchInfo');
        searchInfo.parentNode.insertBefore(enhancementInfo, searchInfo.nextSibling);
    }

    // 使用建议进行搜索
    searchSuggestion(suggestion) {
        document.getElementById('searchInput').value = suggestion;
        this.performSearch(null, true);
    }

    // 显示Gemini状态
    showGeminiStatus() {
        if (!this.geminiAssistant) {
            this.showNotification('⚠️ Gemini AI 助手未初始化\n请检查配置文件', 5000);
            return;
        }

        const status = this.geminiAssistant.getStatus();
        const statusMessage = status.available
            ? `✨ Gemini AI 助手已启用\n模型: ${status.model}\n状态: 正常运行\nFAQ数据: ${status.dataManagerConnected ? '已连接' : '未连接'}`
            : `⚠️ Gemini AI 助手不可用\n请检查API配置`;

        this.showNotification(statusMessage, 5000);
    }
    
    // 显示流式指示器
    showStreamingIndicator(show) {
        let indicator = document.getElementById('streamingIndicator');
        
        if (show && !indicator) {
            indicator = document.createElement('div');
            indicator.id = 'streamingIndicator';
            indicator.className = 'streaming-indicator';
            indicator.innerHTML = `
                <div class="streaming-content">
                    <div class="streaming-dots">
                        <span></span><span></span><span></span>
                    </div>
                    <span class="streaming-text">AI 正在分析...</span>
                </div>
            `;
            
            const searchContainer = document.querySelector('.search-container');
            searchContainer.appendChild(indicator);
        } else if (!show && indicator) {
            indicator.remove();
        }
    }
    


    // 显示反馈
    showFeedback() {
        const feedback = prompt('请输入您的反馈意见：');
        if (feedback && feedback.trim()) {
            // 这里可以发送反馈到服务器
            console.log('用户反馈:', feedback);
            this.showNotification(this.i18n.t('thankYouForFeedback'));
        }
    }

    // 显示通知
    showNotification(message, duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'notification purple-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 隐藏动画
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }
}

// 应用启动
let app;

function initializeApp() {
    // 检查所有必需的依赖是否已加载
    if (typeof I18nManager === 'undefined') {
        console.warn('I18nManager not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    if (typeof DataManager === 'undefined') {
        console.warn('DataManager not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    if (typeof GeminiSearchAssistant === 'undefined') {
        console.warn('GeminiSearchAssistant not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    if (!window.CONFIG) {
        console.warn('CONFIG not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    try {
        app = new FAQApp();
        window.app = app; // 确保全局可访问
        console.log('FAQ App initialized successfully');
    } catch (error) {
        console.error('Failed to initialize FAQ App:', error);
        // 显示错误信息给用户
        document.body.innerHTML = `
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>应用初始化失败</h2>
                <p>请刷新页面重试，或检查控制台获取详细错误信息。</p>
                <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px;">刷新页面</button>
            </div>
        `;
    }
}

document.addEventListener('DOMContentLoaded', initializeApp);
