# GoMyHire 司机FAQ系统

一个现代化的、响应式的司机常见问题解答系统，支持中文、英文、马来文三语切换。

## 🚀 功能特性

### 核心功能
- **智能搜索**: 支持问题编号、关键词、标签搜索
- **多语言支持**: 中文/英文/马来文三语无缝切换
- **响应式设计**: 完美适配手机、平板、桌面设备
- **分类浏览**: 按功能模块组织的清晰分类系统
- **个性化功能**: 收藏夹、浏览历史、热门问题

### 用户体验
- **移动优先**: 专为司机手机使用场景优化
- **快速加载**: 纯前端实现，无需服务器
- **离线可用**: 支持本地缓存和离线浏览
- **直观导航**: 面包屑导航、相关问题推荐
- **便捷分享**: 支持问题链接分享和收藏

## 📱 界面设计

### GoMyHire品牌主题
- **品牌色彩**: 采用GoMyHire官方蓝色主题 (#1a73e8)
- **现代设计**: Material Design风格的卡片和按钮
- **动效交互**: 流畅的过渡动画和悬停效果
- **专业图标**: 自定义SVG logo和功能图标

### 响应式布局
- **桌面端**: 侧边栏导航 + 主内容区域
- **移动端**: 折叠式导航 + 全屏内容显示
- **平板端**: 自适应布局优化

## 🗂️ 内容结构

### 问题分类
1. **司机注册与认证** - 注册流程、材料准备、审核相关
2. **APP使用与技术支持** - 登录问题、功能使用、技术故障
3. **订单管理与调度** - 接单、取消、状态更新
4. **乘客沟通与服务** - 沟通技巧、服务标准、特殊需求
5. **支付与财务管理** - 收入查询、提现、费用结算
6. **应急处理** - 紧急情况、事故报告、安全问题

### 问题优先级
- **高频问题**: 司机最常遇到的核心问题
- **常见问题**: 日常操作中的一般性问题
- **技术问题**: 系统使用和故障排除

## 🛠️ 技术架构

### 前端技术栈
- **HTML5**: 语义化标记和现代Web标准
- **CSS3**: 响应式设计、动画效果、品牌主题
- **JavaScript ES6+**: 模块化架构、现代语法
- **本地存储**: localStorage用于用户偏好和历史记录

### 核心模块
- **I18nManager**: 多语言管理和切换
- **DataManager**: 数据管理和搜索引擎
- **FAQApp**: 主应用逻辑和用户交互

### 数据结构
```javascript
{
  categories: {
    // 分类定义
    "category-id": {
      id: "category-id",
      icon: "🚗",
      name: { zh: "中文名", en: "English", ms: "Bahasa" },
      description: { zh: "描述", en: "Description", ms: "Penerangan" }
    }
  },
  questions: [
    {
      id: "FC-RG-01",
      category: "registration",
      priority: "high",
      title: { zh: "问题标题", en: "Question Title", ms: "Tajuk Soalan" },
      content: { zh: "详细内容", en: "Detailed Content", ms: "Kandungan Terperinci" },
      tags: ["标签1", "tag1", "tag1"],
      relatedQuestions: ["FC-RG-02", "FC-SH-01"]
    }
  ]
}
```

## 📋 基于实际需求的FAQ内容

### 内容来源
- **司机聊天记录分析**: 基于真实的司机-客服对话记录
- **高频问题统计**: 识别最常见的问题和痛点
- **业务流程梳理**: 涵盖完整的司机工作流程

### 实际问题示例
- 携程订单在司机端APP不显示的处理方法
- 误操作接单后的取消流程
- 航班延误时的等待费用申请
- Paging费RM20的申请条件和步骤
- GPS拍照证明的标准操作程序

## 🚀 部署说明

### 静态部署
本系统为纯前端应用，可部署到任何静态文件托管服务：

1. **GitHub Pages**: 免费托管，支持自定义域名
2. **Netlify**: 自动部署，CDN加速
3. **Vercel**: 快速部署，全球CDN
4. **本地服务器**: 任何Web服务器均可

### 部署步骤
1. 将所有文件上传到Web服务器
2. 确保index.html为默认首页
3. 配置HTTPS（推荐）
4. 设置缓存策略优化加载速度

## 📱 使用指南

### 司机用户
1. **快速搜索**: 在顶部搜索框输入问题编号或关键词
2. **分类浏览**: 点击左侧分类导航查看相关问题
3. **收藏问题**: 点击⭐按钮收藏常用问题
4. **语言切换**: 点击右上角语言按钮切换界面语言
5. **分享问题**: 使用分享功能发送问题链接给其他司机

### 管理员
1. **内容更新**: 修改data.js文件中的问题数据
2. **添加分类**: 在categories对象中添加新分类
3. **多语言维护**: 在i18n.js中更新翻译文本
4. **样式定制**: 修改brand-theme.css调整品牌主题

## 🔧 维护和更新

### 内容维护
- 定期收集司机反馈，更新FAQ内容
- 根据新业务流程添加相关问题
- 监控搜索数据，优化问题分类和标签

### 技术维护
- 定期检查浏览器兼容性
- 优化加载性能和用户体验
- 更新品牌设计和视觉元素

## 📞 技术支持

如有技术问题或改进建议，请联系：
- 邮箱：<EMAIL>
- 电话：400-XXX-XXXX

## 📄 许可证

© 2025 GoMyHire. 版权所有。

---

**GoMyHire司机FAQ系统** - 让司机工作更轻松，让问题解决更高效！
