<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire 司机FAQ | Driver FAQ | FAQ Pemandu</title>
    <link rel="stylesheet" href="styles-mobile.css">
    <link rel="icon" type="png" href="gmh logo.png">
</head>
<body>
    <!-- 手机端头部 - 重构为上下两层 -->
    <header class="header">
        <!-- 上层：Logo区域 -->
        <div class="header-top">
            <div class="logo-container">
                <img class="gmh-logo-img" src="gmh logo.png" alt="GMH Logo" />
            </div>
        </div>

        <!-- 下层：搜索和功能区域 -->
        <div class="header-bottom">
            <!-- 搜索区域 (70%) -->
            <div class="search-section">
                <div class="search-input-wrapper">
                    <input type="text" id="searchInput" placeholder="搜索问题..." class="search-input" data-i18n-placeholder="searchPlaceholder">
                    <button id="geminiToggle" class="gemini-toggle active" type="button" title="AI助手已启用">
                        <span class="gemini-icon">✨</span>
                    </button>
                </div>
            </div>

            <!-- 功能按钮区域 (30%) -->
            <div class="controls-section">
                <!-- 主题切换按钮 -->
                <button id="themeToggle" class="theme-toggle" type="button" title="切换主题">
                    <span class="theme-icon">🌙</span>
                </button>

                <!-- 语言切换器 -->
                <div class="language-switcher">
                    <button class="lang-btn active" data-lang="zh" type="button">中</button>
                    <button class="lang-btn" data-lang="en" type="button">EN</button>
                    <button class="lang-btn" data-lang="ms" type="button">MS</button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 欢迎页面 -->
        <div id="welcomePage" class="welcome-page">
            <div class="faq-cards" id="faqCards">
                <!-- FAQ卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 分类页面 -->
        <div id="categoriesPage" class="categories-page page-hidden">
            <div class="page-title">
                <h2>选择分类</h2>
            </div>
            <div id="categories-container" class="categories-container">
                <!-- 分类按钮将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 分类问题页面 -->
        <div id="categoryQuestionsPage" class="category-questions-page page-hidden">
            <!-- 分类问题内容将通过JavaScript动态生成 -->
        </div>

        <!-- FAQ详情页面 -->
        <div id="faqPage" class="faq-page page-hidden">
            <div class="faq-content" id="faqContent">
                <!-- FAQ内容将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 搜索结果页面 -->
        <div id="searchPage" class="search-page page-hidden">
            <div class="search-results" id="searchResults">
                <!-- 搜索结果将通过JavaScript动态生成 -->
            </div>
        </div>
    </main>

    <!-- 手机端底部导航 -->
    <nav class="bottom-nav">
        <a href="#" class="nav-item active" data-tab="faq">
            <span class="icon">📋</span>
            <span>FAQ</span>
        </a>
        <a href="#" class="nav-item" data-tab="chat">
            <span class="icon">💬</span>
            <span>对话</span>
        </a>
    </nav>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="purple-loader"></div>
        <p data-i18n="loading" class="purple-text-gradient">加载中...</p>
    </div>

    <!-- JavaScript文件 -->
    <script src="config.js"></script>
    <script src="gemini-assistant.js"></script>
    <script src="data.js"></script>
    <script src="i18n.js"></script>
    <script src="app.js"></script>
</body>
</html>
