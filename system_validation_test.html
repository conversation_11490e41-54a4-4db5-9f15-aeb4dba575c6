<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire FAQ系统验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 GoMyHire FAQ系统验证测试</h1>
        <p>此页面用于验证FAQ系统的各项功能和数据完整性</p>

        <div class="stats" id="systemStats">
            <div class="stat-card">
                <div class="stat-number" id="totalFAQs">-</div>
                <div class="stat-label">FAQ总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCategories">-</div>
                <div class="stat-label">分类数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="searchTagsCoverage">-</div>
                <div class="stat-label">搜索标签覆盖率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="linkValidation">-</div>
                <div class="stat-label">链接有效性</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 自动化测试</h2>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="runDataIntegrityTest()">数据完整性测试</button>
            <button onclick="runCategorySystemTest()">分类系统测试</button>
            <button onclick="runSearchTagsTest()">搜索标签测试</button>
            <button onclick="runLinkValidationTest()">链接验证测试</button>
            <button onclick="runMultiLanguageTest()">多语言测试</button>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>🔍 详细分析</h2>
            <div id="detailedAnalysis"></div>
        </div>
    </div>

    <!-- 引入必要的脚本文件 -->
    <script src="data.js"></script>
    <script src="i18n.js"></script>
    <script src="config.js"></script>

    <script>
        // 测试结果容器
        const testResults = document.getElementById('testResults');
        const detailedAnalysis = document.getElementById('detailedAnalysis');

        // 日志函数
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            testResults.appendChild(div);
        }

        function logAnalysis(title, content) {
            const div = document.createElement('div');
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            detailedAnalysis.appendChild(div);
        }

        // 更新统计信息
        function updateStats() {
            try {
                const totalFAQs = faqData.questions.length;
                const totalCategories = Object.keys(unifiedCategorySystem).length;
                const searchTagsCount = Object.keys(unifiedSearchTags).length;
                const searchTagsCoverage = Math.round((searchTagsCount / totalFAQs) * 100);

                document.getElementById('totalFAQs').textContent = totalFAQs;
                document.getElementById('totalCategories').textContent = totalCategories;
                document.getElementById('searchTagsCoverage').textContent = searchTagsCoverage + '%';
                document.getElementById('linkValidation').textContent = '检测中...';
            } catch (error) {
                log(`统计信息更新失败: ${error.message}`, 'error');
            }
        }

        // 数据完整性测试
        function runDataIntegrityTest() {
            log('🔍 开始数据完整性测试...', 'info');
            
            try {
                // 检查基本数据结构
                if (!faqData || !faqData.questions) {
                    throw new Error('FAQ数据结构不完整');
                }

                const questions = faqData.questions;
                let issues = [];

                // 检查每个FAQ的完整性
                questions.forEach((faq, index) => {
                    if (!faq.id) issues.push(`FAQ ${index}: 缺少ID`);
                    if (!faq.category) issues.push(`FAQ ${faq.id}: 缺少分类`);
                    if (!faq.priority) issues.push(`FAQ ${faq.id}: 缺少优先级`);
                    if (!faq.title || !faq.title.zh || !faq.title.en || !faq.title.ms) {
                        issues.push(`FAQ ${faq.id}: 多语言标题不完整`);
                    }
                    if (!faq.content || !faq.content.zh || !faq.content.en || !faq.content.ms) {
                        issues.push(`FAQ ${faq.id}: 多语言内容不完整`);
                    }
                    if (!faq.tags || !Array.isArray(faq.tags)) {
                        issues.push(`FAQ ${faq.id}: 标签格式错误`);
                    }
                    if (!faq.relatedQuestions || !Array.isArray(faq.relatedQuestions)) {
                        issues.push(`FAQ ${faq.id}: 相关问题格式错误`);
                    }
                });

                if (issues.length === 0) {
                    log(`✅ 数据完整性测试通过 - 检查了${questions.length}个FAQ`, 'success');
                } else {
                    log(`❌ 数据完整性测试发现${issues.length}个问题`, 'error');
                    logAnalysis('数据完整性问题详情', issues.join('\n'));
                }

            } catch (error) {
                log(`❌ 数据完整性测试失败: ${error.message}`, 'error');
            }
        }

        // 分类系统测试
        function runCategorySystemTest() {
            log('🔍 开始分类系统测试...', 'info');
            
            try {
                const validCategories = Object.keys(unifiedCategorySystem);
                const questions = faqData.questions;
                let invalidCategories = [];
                let categoryStats = {};

                questions.forEach(faq => {
                    if (!validCategories.includes(faq.category)) {
                        invalidCategories.push(`${faq.id}: ${faq.category}`);
                    }
                    categoryStats[faq.category] = (categoryStats[faq.category] || 0) + 1;
                });

                if (invalidCategories.length === 0) {
                    log(`✅ 分类系统测试通过 - 所有FAQ使用标准分类`, 'success');
                    logAnalysis('分类统计', Object.entries(categoryStats)
                        .map(([cat, count]) => `${cat}: ${count}个FAQ`)
                        .join('\n'));
                } else {
                    log(`❌ 分类系统测试发现${invalidCategories.length}个无效分类`, 'error');
                    logAnalysis('无效分类详情', invalidCategories.join('\n'));
                }

            } catch (error) {
                log(`❌ 分类系统测试失败: ${error.message}`, 'error');
            }
        }

        // 搜索标签测试
        function runSearchTagsTest() {
            log('🔍 开始搜索标签测试...', 'info');
            
            try {
                const questions = faqData.questions;
                const searchTags = unifiedSearchTags;
                let missingTags = [];
                let invalidTags = [];

                // 检查缺失的搜索标签
                questions.forEach(faq => {
                    if (!searchTags[faq.id]) {
                        missingTags.push(faq.id);
                    }
                });

                // 检查无效的搜索标签引用
                Object.keys(searchTags).forEach(tagId => {
                    const faqExists = questions.some(faq => faq.id === tagId);
                    if (!faqExists) {
                        invalidTags.push(tagId);
                    }
                });

                const coverage = Math.round(((questions.length - missingTags.length) / questions.length) * 100);
                
                log(`📊 搜索标签覆盖率: ${coverage}% (${questions.length - missingTags.length}/${questions.length})`, 
                    coverage >= 90 ? 'success' : 'warning');

                if (missingTags.length > 0) {
                    logAnalysis('缺失搜索标签的FAQ', missingTags.join('\n'));
                }
                if (invalidTags.length > 0) {
                    logAnalysis('无效的搜索标签引用', invalidTags.join('\n'));
                }

            } catch (error) {
                log(`❌ 搜索标签测试失败: ${error.message}`, 'error');
            }
        }

        // 链接验证测试
        function runLinkValidationTest() {
            log('🔍 开始链接验证测试...', 'info');
            
            try {
                const questions = faqData.questions;
                const faqIds = new Set(questions.map(faq => faq.id));
                let brokenLinks = [];
                let totalLinks = 0;

                questions.forEach(faq => {
                    if (faq.relatedQuestions && Array.isArray(faq.relatedQuestions)) {
                        faq.relatedQuestions.forEach(relatedId => {
                            totalLinks++;
                            if (!faqIds.has(relatedId)) {
                                brokenLinks.push(`${faq.id} → ${relatedId}`);
                            }
                        });
                    }
                });

                const validLinks = totalLinks - brokenLinks.length;
                const linkValidation = Math.round((validLinks / totalLinks) * 100);
                
                document.getElementById('linkValidation').textContent = linkValidation + '%';

                if (brokenLinks.length === 0) {
                    log(`✅ 链接验证测试通过 - 所有${totalLinks}个链接都有效`, 'success');
                } else {
                    log(`❌ 链接验证发现${brokenLinks.length}个断链`, 'error');
                    logAnalysis('断链详情', brokenLinks.join('\n'));
                }

            } catch (error) {
                log(`❌ 链接验证测试失败: ${error.message}`, 'error');
            }
        }

        // 多语言测试
        function runMultiLanguageTest() {
            log('🔍 开始多语言测试...', 'info');
            
            try {
                const questions = faqData.questions;
                const languages = ['zh', 'en', 'ms'];
                let issues = [];

                questions.forEach(faq => {
                    languages.forEach(lang => {
                        if (!faq.title[lang] || faq.title[lang].trim() === '') {
                            issues.push(`${faq.id}: ${lang}语言标题为空`);
                        }
                        if (!faq.content[lang] || faq.content[lang].trim() === '') {
                            issues.push(`${faq.id}: ${lang}语言内容为空`);
                        }
                    });
                });

                if (issues.length === 0) {
                    log(`✅ 多语言测试通过 - 所有FAQ都有完整的三语言内容`, 'success');
                } else {
                    log(`❌ 多语言测试发现${issues.length}个问题`, 'error');
                    logAnalysis('多语言问题详情', issues.join('\n'));
                }

            } catch (error) {
                log(`❌ 多语言测试失败: ${error.message}`, 'error');
            }
        }

        // 运行所有测试
        function runAllTests() {
            testResults.innerHTML = '';
            detailedAnalysis.innerHTML = '';
            
            log('🚀 开始运行完整的系统验证测试...', 'info');
            
            runDataIntegrityTest();
            runCategorySystemTest();
            runSearchTagsTest();
            runLinkValidationTest();
            runMultiLanguageTest();
            
            log('✅ 所有测试完成！请查看上方结果。', 'success');
        }

        // 页面加载时初始化
        window.onload = function() {
            updateStats();
            log('📋 系统验证测试页面已加载，点击按钮开始测试', 'info');
        };
    </script>
</body>
</html>
