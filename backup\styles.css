/* 移动端优化的基础样式重置和变量定义 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;

    /* 移动端优化的颜色变量 */
    --border-light: #e8eaed;
    --divider-color: #f1f3f4;
    --touch-target-size: 44px; /* iOS推荐的最小触摸目标尺寸 */
    --safe-area-top: env(safe-area-inset-top);
    --safe-area-bottom: env(safe-area-inset-bottom);
    --safe-area-left: env(safe-area-inset-left);
    --safe-area-right: env(safe-area-inset-right);

    /* 移动端优化的阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
    --shadow-xl: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
    --shadow-floating: 0 8px 16px rgba(0, 0, 0, 0.15);

    /* 紧凑化响应式尺寸变量 */
    --header-height-mobile: 50px;
    --header-height-tablet: 60px;
    --header-height-desktop: 70px;
    --sidebar-width-mobile: 100vw;
    --sidebar-width-tablet: 280px;
    --sidebar-width-desktop: 260px;
    --content-padding-mobile: 12px;
    --content-padding-tablet: 16px;
    --content-padding-desktop: 20px;

    /* 紧凑化间距变量 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;

    /* 组件间距变量 */
    --section-gap-mobile: 8px;
    --section-gap-tablet: 12px;
    --section-gap-desktop: 16px;
    --component-gap-mobile: 6px;
    --component-gap-tablet: 8px;
    --component-gap-desktop: 12px;

    /* 移动端优化的边框半径 */
    --border-radius-sm: 4px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 50%;

    /* 移动端优化的过渡动画 */
    --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-fast: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-slow: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 移动端字体尺寸 */
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    --font-size-3xl: 1.875rem; /* 30px */
    --font-size-4xl: 2.25rem;  /* 36px */

    /* 移动端行高 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
}

/* 移动端优化的基础样式 */
html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans CJK SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--background-color);
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
    padding-top: var(--safe-area-top);
    padding-bottom: var(--safe-area-bottom);
    padding-left: var(--safe-area-left);
    padding-right: var(--safe-area-right);
}

/* 紧凑化容器 */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--content-padding-mobile);
}

/* 响应式容器 */
@media (min-width: 768px) {
    .container {
        padding: 0 var(--content-padding-tablet);
        max-width: 1400px; /* 增加最大宽度以提高空间利用率 */
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--content-padding-desktop);
    }
}

/* 移动端触摸优化 */
button,
.btn,
.clickable,
[role="button"],
input[type="button"],
input[type="submit"] {
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
}

/* 移动端链接优化 */
a {
    color: var(--primary-color);
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    transition: var(--transition-fast);
}

a:hover, a:focus {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* 移动端输入框优化 */
input, textarea, select {
    font-size: var(--font-size-base);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

/* 防止iOS缩放 */
input[type="text"],
input[type="email"],
input[type="search"],
textarea {
    font-size: 16px; /* 防止iOS Safari缩放 */
}

@media (max-width: 767px) {
    input[type="text"],
    input[type="email"],
    input[type="search"],
    textarea {
        font-size: 16px !important; /* 强制防止缩放 */
    }
}

/* 移动端优化的顶部导航栏 */
.header {
    background: linear-gradient(135deg, var(--surface-color) 0%, #fefbff 100%);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    height: var(--header-height-mobile);
    transition: var(--transition-fast);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0;
    gap: 12px;
}

/* 响应式头部高度 */
@media (min-width: 768px) {
    .header {
        height: var(--header-height-tablet);
    }
}

@media (min-width: 1024px) {
    .header {
        height: var(--header-height-desktop);
    }

    .header-content {
        padding: 15px 0;
    }
}

/* 移动端优化的Logo样式 */
.logo-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0; /* 允许收缩 */
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: auto;
    height: 100%;
    max-height: calc(var(--header-height-mobile) - 16px);
}

.gmh-logo-img {
    height: 32px;
    width: auto;
    max-width: 120px;
    object-fit: contain;
    display: block;
}

.logo-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 8px;
    min-width: 0;
}

.logo-text h1 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    line-height: var(--line-height-tight);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.logo-subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 500;
    line-height: var(--line-height-tight);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 响应式Logo */
@media (min-width: 768px) {
    .logo-container {
        max-height: calc(var(--header-height-tablet) - 20px);
    }

    .gmh-logo-img {
        height: 40px;
        max-width: 160px;
    }

    .logo-text h1 {
        font-size: var(--font-size-xl);
    }

    .logo-subtitle {
        font-size: var(--font-size-sm);
    }
}

@media (min-width: 1024px) {
    .logo-container {
        gap: 12px;
    }

    .logo-container {
        max-height: calc(var(--header-height-desktop) - 30px);
    }

    .gmh-logo-img {
        height: 50px;
        max-width: 200px;
    }

    .logo-text h1 {
        font-size: var(--font-size-2xl);
    }

    .logo-subtitle {
        font-size: var(--font-size-base);
    }
}

/* 移动端隐藏部分Logo文本 */
@media (max-width: 480px) {
    .logo-subtitle {
        display: none;
    }

    .logo-text h1 {
        font-size: var(--font-size-base);
    }

    .gmh-logo-img {
        height: 28px;
        max-width: 100px;
    }
}

/* 移动端优化的语言切换器 */
.language-switcher {
    display: flex;
    gap: 2px;
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 2px;
    box-shadow: var(--shadow-sm);
    flex-shrink: 0;
}

.lang-btn {
    padding: 6px 8px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
    min-height: var(--touch-target-size);
    min-width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
}

.lang-btn:hover, .lang-btn:focus {
    background: var(--hover-color);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.lang-btn:active {
    transform: translateY(0);
    background: var(--primary-light);
}

.lang-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
    transform: none;
}

.lang-btn.active:hover {
    background: var(--primary-dark);
    transform: none;
}

/* 响应式语言切换器 */
@media (min-width: 768px) {
    .language-switcher {
        gap: 4px;
        padding: 4px;
    }

    .lang-btn {
        padding: 8px 12px;
        font-size: var(--font-size-sm);
        min-width: 48px;
    }
}

@media (min-width: 1024px) {
    .lang-btn {
        padding: 8px 16px;
        font-size: var(--font-size-sm);
        min-width: 60px;
    }
}

/* 移动端紧凑模式 */
@media (max-width: 480px) {
    .language-switcher {
        gap: 1px;
        padding: 1px;
    }

    .lang-btn {
        padding: 4px 6px;
        font-size: 10px;
        min-width: 28px;
        min-height: 32px;
    }
}

/* 移动端汉堡菜单按钮 */
.hamburger-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
    order: -1; /* 显示在最左边 */
}

.hamburger-menu:hover, .hamburger-menu:focus {
    background: var(--hover-color);
    transform: scale(1.1);
}

.hamburger-menu:active {
    transform: scale(0.95);
}

.hamburger-icon {
    width: 24px;
    height: 24px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.hamburger-icon span {
    display: block;
    width: 100%;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: var(--transition-fast);
    transform-origin: center;
}

.hamburger-icon span:nth-child(1) {
    transform: translateY(0);
}

.hamburger-icon span:nth-child(2) {
    opacity: 1;
}

.hamburger-icon span:nth-child(3) {
    transform: translateY(0);
}

/* 汉堡菜单激活状态 */
.hamburger-menu.active .hamburger-icon span:nth-child(1) {
    transform: translateY(10px) rotate(45deg);
}

.hamburger-menu.active .hamburger-icon span:nth-child(2) {
    opacity: 0;
}

.hamburger-menu.active .hamburger-icon span:nth-child(3) {
    transform: translateY(-10px) rotate(-45deg);
}

/* 响应式汉堡菜单 */
@media (min-width: 768px) {
    .hamburger-menu {
        display: none;
    }
}

/* 移动端触摸反馈样式 */
.touch-active {
    transform: scale(0.95) !important;
    opacity: 0.8 !important;
    transition: all 0.1s ease !important;
}

.category-link.touch-active {
    background: var(--primary-light) !important;
    transform: translateX(2px) scale(0.98) !important;
}

.lang-btn.touch-active {
    background: var(--primary-light) !important;
    transform: scale(0.9) !important;
}

.search-btn.touch-active {
    background: var(--primary-dark) !important;
    transform: scale(0.9) !important;
}

/* 虚拟键盘适配 */
body.keyboard-open {
    height: 100vh;
    overflow: hidden;
}

body.keyboard-open .main-content {
    padding-bottom: 50vh; /* 为虚拟键盘留出空间 */
}

/* 移动端滚动优化 */
.sidebar,
.content-area {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* 移动端性能优化 */
.category-link,
.lang-btn,
.search-btn,
.hamburger-menu,
.toggle-btn {
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* 移动端安全区域适配 */
@supports (padding: max(0px)) {
    .header {
        padding-left: max(var(--safe-area-left), 0px);
        padding-right: max(var(--safe-area-right), 0px);
    }

    .sidebar {
        padding-left: max(var(--safe-area-left), 0px);
    }

    .main-content {
        padding-left: max(var(--safe-area-left), 0px);
        padding-right: max(var(--safe-area-right), 0px);
    }
}

/* 紧凑化搜索栏 */
.search-container {
    display: flex;
    gap: var(--component-gap-mobile);
    margin-top: var(--spacing-sm);
    width: 100%;
    align-items: stretch;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    background: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
    min-height: var(--touch-target-size);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1), var(--shadow-md);
    transform: translateY(-1px);
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    padding-right: 60px;
    border: none;
    background: transparent;
    font-size: 16px; /* 防止iOS缩放 */
    color: var(--text-primary);
    outline: none;
    border-radius: var(--border-radius);
    line-height: var(--line-height-normal);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.search-input::placeholder {
    color: var(--text-secondary);
    font-weight: 400;
    opacity: 0.8;
}

.search-input:focus::placeholder {
    opacity: 0.5;
}

/* 响应式搜索栏 */
@media (min-width: 768px) {
    .search-container {
        gap: var(--component-gap-tablet);
        margin-top: var(--spacing-md);
        max-width: 700px; /* 增加最大宽度 */
        margin-left: auto;
        margin-right: auto;
    }
}

@media (min-width: 1024px) {
    .search-container {
        gap: var(--component-gap-desktop);
        margin-top: var(--spacing-lg);
        max-width: 800px; /* 进一步增加最大宽度 */
    }
}

/* 移动端紧凑搜索栏 */
@media (max-width: 480px) {
    .search-container {
        gap: 6px;
        margin-top: 8px;
    }

    .search-input {
        padding: 10px 12px;
        padding-right: 50px;
        font-size: 16px; /* 保持16px防止缩放 */
    }

    .search-input-wrapper {
        min-height: 40px;
    }
}

/* 移动端优化的Gemini切换按钮 */
.gemini-toggle {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    padding: 4px 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-xs);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 2px;
    transition: var(--transition-fast);
    z-index: 2;
    min-height: 32px;
    min-width: 40px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
}

.gemini-toggle:hover, .gemini-toggle:focus {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-50%) scale(1.05);
}

.gemini-toggle:active {
    transform: translateY(-50%) scale(0.95);
}

.gemini-toggle.active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.gemini-icon {
    font-size: var(--font-size-sm);
    animation: sparkle 2s ease-in-out infinite;
    will-change: transform, opacity;
}

/* 优化动画性能 */
@keyframes sparkle {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* 移动端优化的搜索按钮 */
.search-btn {
    padding: 0;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    font-size: var(--font-size-xl);
    transition: var(--transition-fast);
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
    box-shadow: var(--shadow-sm);
}

.search-btn:hover, .search-btn:focus {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.search-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* 响应式按钮 */
@media (min-width: 768px) {
    .gemini-toggle {
        right: 8px;
        padding: 6px 10px;
        gap: 4px;
        min-height: 36px;
        min-width: 48px;
    }

    .search-btn {
        padding: 12px 20px;
        min-width: auto;
        font-size: var(--font-size-lg);
    }
}

@media (min-width: 1024px) {
    .search-btn {
        font-size: var(--font-size-xl);
    }
}

/* 移动端紧凑按钮 */
@media (max-width: 480px) {
    .gemini-toggle {
        right: 4px;
        padding: 3px 6px;
        min-height: 28px;
        min-width: 32px;
        font-size: 10px;
    }

    .gemini-icon {
        font-size: 12px;
    }

    .search-btn {
        min-height: 40px;
        min-width: 40px;
        font-size: var(--font-size-lg);
    }
}

/* 搜索增强信息 */
.search-enhancement-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    margin: 15px 0;
    box-shadow: var(--shadow-sm);
}

.enhancement-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    margin-bottom: 10px;
}

.enhancement-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.suggestion-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: var(--transition);
}

.suggestion-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 流式指示器 */
.streaming-indicator {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-sm);
    z-index: 10;
    margin-top: 2px;
}

.search-container {
    position: relative;
}

.streaming-content {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 0.875rem;
}

.streaming-dots {
    display: flex;
    gap: 4px;
}

.streaming-dots span {
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: streamingPulse 1.5s ease-in-out infinite;
}

.streaming-dots span:nth-child(1) {
    animation-delay: 0s;
}

.streaming-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.streaming-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes streamingPulse {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1);
    }
}

.streaming-text {
    font-weight: 500;
    opacity: 0.9;
}

/* 紧凑化主要内容区域 */
.main-content {
    min-height: calc(100vh - var(--header-height-mobile) - 40px);
    padding: var(--spacing-sm) 0;
    position: relative;
}

.content-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--section-gap-mobile);
    position: relative;
}

/* 响应式内容区域 */
@media (min-width: 768px) {
    .main-content {
        min-height: calc(100vh - var(--header-height-tablet) - 50px);
        padding: var(--spacing-md) 0;
    }

    .content-wrapper {
        display: grid;
        grid-template-columns: var(--sidebar-width-tablet) 1fr;
        gap: var(--section-gap-tablet);
        align-items: start;
    }
}

@media (min-width: 1024px) {
    .main-content {
        min-height: calc(100vh - var(--header-height-desktop) - 60px);
        padding: var(--spacing-lg) 0;
    }

    .content-wrapper {
        grid-template-columns: var(--sidebar-width-desktop) 1fr;
        gap: var(--section-gap-desktop);
    }
}

/* 移动端优化的侧边栏 */
.sidebar {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: var(--header-height-mobile);
    left: 0;
    width: var(--sidebar-width-mobile);
    height: calc(100vh - var(--header-height-mobile));
    z-index: 999;
    transform: translateX(-100%);
    transition: var(--transition);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.sidebar.open {
    transform: translateX(0);
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--content-padding-mobile);
    border-bottom: 1px solid var(--border-color);
    background: var(--surface-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.sidebar-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--text-secondary);
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
}

.toggle-btn:hover, .toggle-btn:focus {
    background: var(--hover-color);
    color: var(--text-primary);
    transform: scale(1.1);
}

.toggle-btn:active {
    transform: scale(0.95);
}

/* 移动端侧边栏遮罩 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 响应式侧边栏 */
@media (min-width: 768px) {
    .sidebar {
        position: sticky;
        top: calc(var(--header-height-tablet) + 20px);
        left: auto;
        width: auto;
        height: auto;
        max-height: calc(100vh - var(--header-height-tablet) - 40px);
        transform: none;
        z-index: auto;
    }

    .sidebar-header {
        padding: 20px;
    }

    .toggle-btn {
        display: none;
    }

    .sidebar-overlay {
        display: none;
    }
}

@media (min-width: 1024px) {
    .sidebar {
        top: calc(var(--header-height-desktop) + 20px);
        max-height: calc(100vh - var(--header-height-desktop) - 40px);
    }
}

/* 紧凑化分类导航 */
.category-nav {
    padding: var(--spacing-sm);
    padding-top: 0;
}

.category-item {
    margin-bottom: var(--spacing-xs);
}

.category-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    min-height: 36px; /* 紧凑化触摸目标 */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
    position: relative;
    overflow: hidden;
}

.category-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    opacity: 0;
    transition: var(--transition-fast);
    z-index: -1;
}

.category-link:hover::before,
.category-link:focus::before {
    opacity: 0.1;
}

.category-link:active::before {
    opacity: 0.2;
}

.category-link:hover,
.category-link:focus,
.category-link.active {
    background: var(--primary-color);
    color: white;
    transform: translateX(4px);
}

.category-link.active::before {
    opacity: 1;
}

.category-icon {
    margin-right: var(--spacing-sm);
    font-size: var(--font-size-base);
    flex-shrink: 0;
    width: 20px;
    text-align: center;
}

.category-name {
    flex: 1;
    font-weight: 500;
    line-height: var(--line-height-tight);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.category-count {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    min-width: 24px;
    text-align: center;
    flex-shrink: 0;
    transition: var(--transition-fast);
}

.category-link:hover .category-count,
.category-link:focus .category-count,
.category-link.active .category-count {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* 响应式分类导航 */
@media (min-width: 768px) {
    .category-nav {
        padding: var(--spacing-lg);
    }

    .category-item {
        margin-bottom: var(--spacing-xs);
    }

    .category-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        min-height: 38px;
    }

    .category-icon {
        margin-right: var(--spacing-sm);
        font-size: var(--font-size-base);
        width: 18px;
    }
}

@media (min-width: 1024px) {
    .category-nav {
        padding: var(--spacing-xl);
    }

    .category-item {
        margin-bottom: var(--spacing-sm);
    }

    .category-link {
        min-height: 40px;
    }
}

/* 移动端紧凑分类导航 */
@media (max-width: 480px) {
    .category-nav {
        padding: 12px;
    }

    .category-link {
        padding: 10px 12px;
        font-size: var(--font-size-sm);
    }

    .category-icon {
        margin-right: 8px;
        font-size: var(--font-size-base);
        width: 20px;
    }

    .category-count {
        padding: 2px 6px;
        min-width: 20px;
        font-size: 10px;
    }
}

/* 快速访问 */
.quick-access {
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.quick-access h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 15px;
}

.quick-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quick-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: calc(var(--border-radius) - 2px);
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-secondary);
    transition: var(--transition);
}

.quick-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.count {
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 紧凑化内容区域 */
.content-area {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    min-height: calc(100vh - var(--header-height-mobile) - 80px);
    overflow: hidden;
    position: relative;
}

/* 响应式内容区域 */
@media (min-width: 768px) {
    .content-area {
        min-height: calc(100vh - var(--header-height-tablet) - 90px);
    }
}

@media (min-width: 1024px) {
    .content-area {
        min-height: calc(100vh - var(--header-height-desktop) - 100px);
    }
}

/* 紧凑化欢迎页面 */
.welcome-page {
    padding: var(--spacing-lg);
    text-align: center;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--surface-color) 50%, #faf5ff 100%);
    position: relative;
    overflow: hidden;
    min-height: 100%;
}

/* 响应式欢迎页面 */
@media (min-width: 768px) {
    .welcome-page {
        padding: var(--spacing-xl);
    }
}

@media (min-width: 1024px) {
    .welcome-page {
        padding: var(--spacing-2xl);
    }
}

.welcome-page::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(184, 61, 186, 0.08) 0%, rgba(217, 70, 239, 0.05) 50%, transparent 70%);
    animation: float 20s ease-in-out infinite;
    z-index: 0;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.welcome-page h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    line-height: var(--line-height-tight);
}

.welcome-page p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 700px; /* 增加最大宽度 */
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    line-height: var(--line-height-normal);
}

/* 功能卡片 */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.feature-card {
    padding: 24px;
    background: var(--surface-color);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.feature-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--primary-light), #faf5ff);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(184, 61, 186, 0.15);
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.feature-card:hover .feature-icon::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 紧凑化快速开始按钮 */
.quick-start {
    margin-top: var(--spacing-lg);
}

.quick-start h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: var(--line-height-tight);
}

.quick-start-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: var(--spacing-sm);
    justify-content: center;
    max-width: 700px; /* 增加最大宽度 */
    margin: 0 auto;
}

/* 响应式快速开始按钮 */
@media (min-width: 768px) {
    .quick-start {
        margin-top: var(--spacing-xl);
    }

    .quick-start h3 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-lg);
    }

    .quick-start-buttons {
        gap: var(--spacing-md);
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        max-width: 800px;
    }
}

@media (min-width: 1024px) {
    .quick-start {
        margin-top: var(--spacing-2xl);
    }

    .quick-start h3 {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-xl);
    }

    .quick-start-buttons {
        grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
        max-width: 900px;
    }
}

/* 紧凑化按钮样式 */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    white-space: nowrap;
    min-height: 36px; /* 紧凑化触摸目标 */
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
    -webkit-user-select: none;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: var(--transition-fast);
    z-index: 1;
}

.btn:hover::before,
.btn:focus::before {
    opacity: 1;
}

.btn:active::before {
    opacity: 0.2;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover,
.btn-primary:focus {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-outline:hover,
.btn-outline:focus {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* 响应式按钮 */
@media (min-width: 768px) {
    .btn {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--font-size-base);
        min-height: 38px;
    }
}

@media (min-width: 1024px) {
    .btn {
        padding: var(--spacing-md) var(--spacing-2xl);
        font-size: var(--font-size-base);
        min-height: 40px;
    }
}

/* 移动端超紧凑按钮 */
@media (max-width: 480px) {
    .btn {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--font-size-xs);
        min-height: 32px;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .sidebar {
        position: relative;
        top: auto;
        max-height: none;
    }
    
    .toggle-btn {
        display: block;
    }
    
    .category-nav {
        display: none;
    }
    
    .category-nav.show {
        display: block;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-container {
        margin-top: 0;
        width: 100%;
    }
    
    .welcome-page {
        padding: 20px;
    }
    
    .welcome-page h2 {
        font-size: 1.5rem;
    }
    
    .feature-cards {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .quick-start-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* FAQ页面样式 */
.faq-page {
    padding: 30px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb-separator {
    color: var(--text-secondary);
}

/* 紧凑化FAQ内容 */
.faq-content {
    margin-bottom: var(--spacing-2xl);
}

.faq-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.faq-id {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.faq-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.3;
}

.faq-priority {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 12px;
}

.priority-high {
    background: #fef2f2;
    color: var(--danger-color);
}

.priority-medium {
    background: #fef3c7;
    color: var(--warning-color);
}

.priority-low {
    background: #f0f9ff;
    color: var(--primary-color);
}

.faq-body {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-primary);
}

.faq-body h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 24px 0 12px 0;
}

.faq-body h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 20px 0 10px 0;
}

.faq-body ul, .faq-body ol {
    margin: 16px 0;
    padding-left: 24px;
}

.faq-body li {
    margin-bottom: 8px;
}

.faq-body table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.faq-body th,
.faq-body td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.faq-body th {
    background: var(--background-color);
    font-weight: 600;
    color: var(--text-primary);
}

.faq-body tr:last-child td {
    border-bottom: none;
}

.warning-box {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: var(--border-radius);
    padding: 16px;
    margin: 20px 0;
}

.warning-box::before {
    content: "⚠️ ";
    font-weight: bold;
}

.info-box {
    background: #f0f9ff;
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 16px;
    margin: 20px 0;
}

.info-box::before {
    content: "ℹ️ ";
    font-weight: bold;
}

/* 相关问题 */
.related-questions {
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.related-questions h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.related-list {
    display: grid;
    gap: 12px;
}

.related-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
}

.related-item:hover {
    background: var(--primary-color);
    color: white;
}

.related-item-id {
    background: var(--surface-color);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 12px;
    min-width: 60px;
    text-align: center;
}

.related-item:hover .related-item-id {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 30px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.action-buttons .btn {
    flex: 1;
    justify-content: center;
}

/* 搜索结果页面 */
.search-page {
    padding: 30px;
}

.search-page h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.search-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.search-results {
    display: grid;
    gap: 16px;
}

.search-result-item {
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.search-result-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.search-result-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.search-result-id {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 12px;
}

.search-result-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

.search-result-category {
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
}

.search-result-excerpt {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 8px;
}

.search-highlight {
    background: #fef3c7;
    color: var(--warning-color);
    font-weight: 600;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 分类页面 */
.category-page {
    padding: 30px;
}

.category-page h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.category-description {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
}

.category-questions {
    display: grid;
    gap: var(--spacing-sm);
}

.question-item {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.question-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.question-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.question-id {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    margin-right: var(--spacing-sm);
}

.question-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

.question-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.question-summary {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 8px;
}

/* 底部 */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.footer-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.footer-section p,
.footer-section a {
    color: #cbd5e1;
    text-decoration: none;
    margin-bottom: 8px;
    display: block;
}

.footer-section a:hover {
    color: white;
}

.copyright {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* 紧凑化返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 44px;
    height: 44px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-lg);
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
}

/* 加载提示 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .welcome-page {
        padding: 15px;
    }

    .feature-card {
        padding: 16px;
    }

    .language-switcher {
        width: 100%;
        justify-content: center;
    }

    .faq-page,
    .search-page,
    .category-page {
        padding: 20px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .back-to-top {
        bottom: var(--spacing-md);
        right: var(--spacing-md);
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
}
